import { Routes } from '@angular/router';
import { customerTemplateGuard } from './guards/customer-template.guard';
import { userListGuard } from './guards/user-list.guard';

export const ROUTES: Routes = [
  {
    path: '',
    pathMatch: 'full',
    redirectTo: 'customers',
  },
  {
    path: 'customers',
    loadComponent: () =>
      import('./components/customer-list/customer-list.component').then(
        (m) => m.CustomerListComponent,
      ),
  },
  {
    path: 'customers/:selectedCustomerKey/users',
    loadComponent: () =>
      import('./components/user-list/user-list.component').then(
        (m) => m.UserListComponent,
      ),
    canActivate: [userListGuard],
  },
  {
    path: 'customers/:selectedCustomerKey/template-management',
    loadComponent: () =>
      import(
        './components/customer-templates/customer-templates.component'
      ).then((m) => m.CustomerTemplatesComponent),
    canActivate: [customerTemplateGuard],
  },
  {
    path: 'usage-contracts',
    loadComponent: () =>
      import(
        './components/usage-contract-list/usage-contract-list.component'
      ).then((m) => m.UsageContractListComponent),
  },
];
