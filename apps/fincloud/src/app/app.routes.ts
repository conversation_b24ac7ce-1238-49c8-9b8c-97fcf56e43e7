import { Route } from '@angular/router';
import {
  alreadyAcceptedTermsAndConditionsGuard,
  authGuard,
  dashboardRedirectGuard,
  demoEnvironmentGuard,
  initialLoginGuard,
  internalCustomerGuard,
  invitedGuestGuard,
  loadIsDemoEnvironmentGuard,
  loadUserGuard,
  localeGuard,
  localeUserGuard,
  magicLinkGuard,
  permissionGuard,
  termsAndConditionsGuard,
  themeGuard,
} from '@fincloud/neoshare/guards';
import { isTodosManagementAvailableGuard } from '@fincloud/neoshare/todos-management';
import { Page, Permission, UserRole } from '@fincloud/types/enums';
import { canDeactivateBusinessCaseGuard } from './business-case-dashboard/guards/can-deactivate-business-case.guard';

export const appRoutes: Route[] = [
  {
    path: 'login',
    loadChildren: () =>
      import('./login/login.module').then((m) => m.LoginModule),
    canActivate: [localeGuard],
  },
  {
    path: ':customerKey/:username/magic-link',
    canActivate: [magicLinkGuard],
    children: [
      {
        path: 'sign',
        loadChildren: () =>
          import('./user-signing/user-signing.module').then(
            (m) => m.UserSigningModule,
          ),
      },
    ],
  },
  {
    path: ':customerKey',
    loadComponent: () =>
      import('./layout/components/main-layout/main-layout.component').then(
        (m) => m.MainLayoutComponent,
      ),
    canActivate: [
      authGuard,
      initialLoginGuard,
      loadUserGuard,
      termsAndConditionsGuard,
      invitedGuestGuard,
      localeUserGuard,
      themeGuard,
      loadIsDemoEnvironmentGuard,
    ],
    children: [
      {
        path: '',
        canActivate: [dashboardRedirectGuard, permissionGuard],
        loadComponent: () =>
          import('./layout/components/main-layout/main-layout.component').then(
            (m) => m.MainLayoutComponent,
          ),
        data: {
          permissions: {
            only: [
              Permission.PERM_0001,
              Permission.PERM_0002,
              Permission.PERM_0059,
              UserRole.ACCOUNT_MANAGER,
              UserRole.LEGAL_OFFICER,
              UserRole.USAGE_CONTRACT_SIGNER,
            ],
          },
        },
      },
      {
        path: 'dashboard',
        canMatch: [permissionGuard],
        data: {
          page: Page.DASHBOARD,
          permissions: {
            only: [Permission.PERM_0059],
            redirectTo: '/digital-signature',
          },
        },
        loadChildren: () =>
          import('./dashboard/dashboard.module').then((m) => m.DashboardModule),
      },
      {
        path: 'cases',
        canMatch: [permissionGuard],
        data: {
          page: Page.CASES,
          permissions: {
            only: [Permission.PERM_0001, Permission.PERM_0002],
            redirectTo: '/digital-signature',
          },
        },
        loadChildren: () =>
          import('./cases/cases.module').then((m) => m.CasesModule),
      },
      {
        path: 'user-management',
        canMatch: [permissionGuard],
        data: {
          page: Page.USER_MANAGEMENT,
          permissions: {
            only: [Permission.PERM_0006],
          },
        },
        loadChildren: () =>
          import('./user-management/user-management.module').then(
            (m) => m.UserManagementModule,
          ),
      },
      {
        path: 'template-management',
        canMatch: [permissionGuard],
        data: {
          page: Page.TEMPLATES_MANAGEMENT,
          permissions: {
            only: [Permission.PERM_0009],
          },
        },
        loadChildren: () =>
          import(
            './business-case-template-management/business-case-template-management.module'
          ).then((m) => m.BusinessCaseTemplateManagementModule),
      },
      {
        path: 'customer-master-data',
        canMatch: [permissionGuard],
        data: {
          page: Page.CUSTOMER_MASTER_DATA,
          permissions: {
            only: [
              Permission.PERM_0007,
              Permission.PERM_0008,
              Permission.PERM_0063,
              Permission.PERM_0064,
            ],
          },
        },
        loadChildren: () =>
          import('./customer-master-data/customer-master-data.module').then(
            (m) => m.CustomerMasterDataModule,
          ),
      },
      {
        path: 'business-case',
        canMatch: [permissionGuard],
        canDeactivate: [canDeactivateBusinessCaseGuard],
        data: {
          page: Page.BUSINESS_CASE,
          permissions: {
            only: [Permission.PERM_0042],
          },
        },
        loadChildren: () =>
          import(
            './business-case-dashboard/business-case-dashboard.module'
          ).then((m) => m.BusinessCaseDashboardModule),
      },
      {
        path: 'company-analysis',
        canMatch: [permissionGuard],
        data: {
          page: Page.COMPANY_ANALYSIS,
          permissions: {
            only: [Permission.PERM_0004, Permission.PERM_0058],
          },
        },
        loadChildren: () =>
          import('./company-analysis/company-analysis.module').then(
            (m) => m.CompanyAnalysisModule,
          ),
      },
      {
        path: 'company-management',
        canMatch: [permissionGuard],
        data: {
          page: Page.COMPANY_MANAGEMENT,
          permissions: {
            only: [Permission.PERM_0004],
          },
        },
        loadChildren: () =>
          import('./company-management/company-management.module').then(
            (m) => m.CompanyManagementModule,
          ),
      },
      {
        path: 'contract-management',
        canMatch: [permissionGuard],
        data: {
          page: Page.CONTRACT_MANAGEMENT,
          permissions: {
            only: [
              Permission.PERM_0052,
              Permission.PERM_0024,
              Permission.PERM_0025,
            ],
          },
        },
        loadChildren: () =>
          import('./contract-management/contract-management.module').then(
            (m) => m.ContractManagementModule,
          ),
      },
      {
        path: 'create-business-case',
        canMatch: [permissionGuard],
        data: {
          page: Page.CREATE_BUSINESS_CASE,
          permissions: {
            only: () => [Permission.PERM_0010],
          },
        },
        loadChildren: () =>
          import('./create-business-case/create-business-case.module').then(
            (m) => m.CreateBusinessCaseModule,
          ),
      },
      {
        path: 'user-settings',
        loadChildren: () =>
          import('./user-settings/user-settings.module').then(
            (m) => m.UserSettingsModule,
          ),
      },
      {
        path: 'billing-management',
        canMatch: [permissionGuard],
        data: {
          page: Page.BILLING_MANAGEMENT,
          permissions: {
            only: [Permission.PERM_0000],
          },
        },
        loadChildren: () =>
          import('./voluntary-payment/voluntary-payment.module').then(
            (m) => m.VoluntaryPaymentModule,
          ),
      },
      {
        path: 'digital-signature',
        canMatch: [permissionGuard],
        data: {
          page: Page.SIGNATURE_BOARD,
          permissions: {
            only: [Permission.PERM_0032],
          },
        },
        loadChildren: () =>
          import('./digital-signature/digital-signature.module').then(
            (m) => m.DigitalSignatureModule,
          ),
      },
      {
        path: 'account-management',
        canMatch: [permissionGuard],
        data: {
          page: Page.KEY_ACCOUNT_MANAGEMENT,
          permissions: {
            only: [
              Permission.PERM_0013,
              Permission.PERM_0014,
              Permission.PERM_0015,
              Permission.PERM_0016,
              Permission.PERM_0017,
              Permission.PERM_0018,
              Permission.PERM_0019,
              Permission.PERM_0020,
              Permission.PERM_0021,
              Permission.PERM_0022,
              Permission.PERM_0023,
            ],
          },
        },
        loadChildren: () =>
          import('./account-management/account-management.module').then(
            (m) => m.AccountManagementModule,
          ),
      },
      {
        path: 'apps-integration',
        canMatch: [permissionGuard],
        data: {
          page: Page.APPS_INTEGRATION,
          permissions: {
            only: [Permission.PERM_0012],
          },
        },
        loadChildren: () =>
          import('./apps-integration/apps-integration.module').then(
            (m) => m.AppsIntegrationModule,
          ),
      },
      {
        path: 'todos-management',
        canActivate: [isTodosManagementAvailableGuard],
        loadChildren: () =>
          import('./todos-management/todos-management.module').then(
            (m) => m.TodosManagementModule,
          ),
      },
      {
        path: 'demo-snapshot',
        canActivate: [internalCustomerGuard],
        canMatch: [demoEnvironmentGuard],
        data: {
          page: Page.SINGLE_CLUSTER_DEMO,
        },
        loadChildren: () =>
          import('./cluster-demo/cluster-demo.module').then(
            (m) => m.SingleClusterDemoModule,
          ),
      },
    ],
  },
  {
    path: ':customerKey/guest',
    canActivate: [
      authGuard,
      initialLoginGuard,
      loadUserGuard,
      invitedGuestGuard,
    ],
    children: [
      {
        path: 'accept-terms',
        loadChildren: () =>
          import('./terms-and-conditions/terms-and-conditions.module').then(
            (m) => m.TermsAndConditionsModule,
          ),
      },
      {
        path: 'invitation',
        loadChildren: () =>
          import('./support/support.module').then((m) => m.SupportModule),
      },
    ],
  },
  {
    path: ':customerKey/privacy-policy',
    canActivate: [
      authGuard,
      initialLoginGuard,
      loadUserGuard,
      alreadyAcceptedTermsAndConditionsGuard,
    ],
    loadChildren: () =>
      import('./consent/consent.module').then((m) => m.ConsentModule),
  },
  {
    path: '**',
    pathMatch: 'full',
    redirectTo: 'customerKey',
  },
];
