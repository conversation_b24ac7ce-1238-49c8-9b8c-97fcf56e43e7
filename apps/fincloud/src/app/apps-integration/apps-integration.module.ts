import { NgModule } from '@angular/core';
import { APPS_INTEGRATIONS_FEATURE_KEY } from '@fincloud/state/utils';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import * as effects from './+state/effects';
import { appIntegrationsReducer } from './+state/reducers/apps-integrations.reducer';

@NgModule({
  imports: [],
  exports: [],
  providers: [
    provideState(APPS_INTEGRATIONS_FEATURE_KEY, appIntegrationsReducer),
    provideEffects(effects.AppsIntegrationsEffects),
  ],
})
export class AppsIntegrationModule {}
