import { Routes } from '@angular/router';
import { appsIntegrationGuard } from './guards/apps-integration.guard';

export const ROUTES: Routes = [
  {
    path: '',
    canActivate: [appsIntegrationGuard],
    loadComponent: () =>
      import('./components/apps-integration/apps-integration.component').then(
        (m) => m.AppsIntegrationComponent,
      ),
  },
  {
    path: 'dracoon-integration',
    canActivate: [appsIntegrationGuard],
    loadComponent: () =>
      import(
        './components/dracoon-integration/dracoon-integration.component'
      ).then((m) => m.DracoonIntegrationComponent),
  },
  {
    path: 'nextfolder-integration',
    canActivate: [appsIntegrationGuard],
    loadComponent: () =>
      import(
        './components/nextfolder-integration/nextfolder-integration.component'
      ).then((m) => m.NextfolderIntegrationComponent),
  },
  {
    path: 'core-banking-integration',
    loadComponent: () =>
      import(
        './components/core-banking-integration/core-banking-integration.component'
      ).then((m) => m.CoreBankingIntegrationComponent),
  },
];
