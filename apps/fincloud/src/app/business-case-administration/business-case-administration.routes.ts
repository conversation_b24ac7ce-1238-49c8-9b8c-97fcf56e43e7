import { Routes } from '@angular/router';
import { faqDataResolver } from '@fincloud/neoshare/guards';
import { administrationIntegrationsActivateGuard } from './guards/administration-integrations-activate.guard';

export const ROUTES: Routes = [
  {
    path: '',
    loadComponent: () =>
      import('./components/administration/administration.component').then(
        (m) => m.AdministrationComponent,
      ),
    pathMatch: 'prefix',
    children: [
      {
        path: '',
        redirectTo: 'users',
        pathMatch: 'full',
      },
      {
        path: 'users',
        loadComponent: () =>
          import(
            './components/administration-users-page/administration-users.component'
          ).then((m) => m.AdministrationUsersComponent),
      },
      {
        path: 'faq',
        loadComponent: () =>
          import(
            './components/administration-faq-page/administration-faq.component'
          ).then((m) => m.AdministrationFaqComponent),
        resolve: [faqDataResolver],
      },
      {
        path: 'case-settings',
        loadComponent: () =>
          import(
            './components/administration-case-settings-page/administration-case-settings.component'
          ).then((m) => m.AdministrationCaseSettingsComponent),
      },
      {
        path: 'integrations',
        loadComponent: () =>
          import(
            './components/administration-integrations/administration-integrations.component'
          ).then((m) => m.AdministrationIntegrationsComponent),
        canActivate: [administrationIntegrationsActivateGuard],
      },
    ],
  },
];
