import { Routes } from '@angular/router';
import { canAccessMyPartnerGuard } from './guards/can-access-my-partner.guard';
import { privateCollaborationInvitationsApplicationsGuard } from './guards/private-collaboration-invitations-applications.guard';
import { privateCollaborationGuard } from './guards/private-collaboration.guard';

export const ROUTES: Routes = [
  {
    path: '',
    loadComponent: () =>
      import(
        './components/collaboration/business-case-collaboration.component'
      ).then((m) => m.BusinessCaseCollaborationComponent),
    canActivate: [privateCollaborationGuard],
    children: [
      {
        path: '',
        redirectTo: 'my-partners',
        pathMatch: 'full',
      },
      {
        path: 'my-partners',
        loadComponent: () =>
          import(
            './components/collaboration-my-partners/collaboration-my-partners.component'
          ).then((m) => m.CollaborationMyPartnersComponent),
        canActivate: [canAccessMyPartnerGuard],
        children: [
          {
            path: ':participantKey',
            loadComponent: () =>
              import('@fincloud/neoshare/business-case').then(
                (m) => m.BusinessCaseParticipantRolesComponent,
              ),
          },
        ],
      },
      {
        path: 'invitations-applications',
        loadComponent: () =>
          import(
            './components/collaboration-applications-invitations/collaboration-applications-invitations.component'
          ).then((m) => m.CollaborationApplicationsInvitationsComponent),
        canActivate: [privateCollaborationInvitationsApplicationsGuard],
      },
    ],
  },
];
