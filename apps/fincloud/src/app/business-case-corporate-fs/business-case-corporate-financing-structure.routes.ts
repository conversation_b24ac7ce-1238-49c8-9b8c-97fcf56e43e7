import { Routes } from '@angular/router';
import { participationRouteGuard } from '@fincloud/neoshare/guards';
import {
  FinancingDetailsPath,
  FinancingDetailsSubPage,
} from '@fincloud/types/enums';
import { corporateFinancingStructureResolver } from './guards/corporate-financing-sructure-resolver';
import { financingDetailsCorporateActivateGuard } from './guards/financing-details-corporate-activate.guard';
import { financingDetailsCorporateDeactivateGuard } from './guards/financing-details-corporate-deactivate.guard';
import { financingStructureCorporateActivateGuard } from './guards/financing-structure-corporate-activate.guard';
import { participationCorporateMatchGuard } from './guards/participation-corporate-match.guard';
import { sectionApplicationCorporateMatchGuard } from './guards/section-application-corporate-match.guard';
import { sectionInvitationCorporateMatchGuard } from './guards/section-invitation-corporate-match.guard';

export const ROUTES: Routes = [
  {
    path: '',
    loadComponent: () =>
      import(
        './components/financing-details-corporate/financing-details-corporate.component'
      ).then((m) => m.FinancingDetailsCorporateComponent),
    canActivate: [financingDetailsCorporateActivateGuard],
    canDeactivate: [financingDetailsCorporateDeactivateGuard],
    children: [
      {
        path: FinancingDetailsPath.FINANCING_STRUCTURE,
        loadComponent: () =>
          import(
            './components/financing-structure/financing-structure.component'
          ).then((m) => m.FinancingStructureComponent),
        canActivate: [financingStructureCorporateActivateGuard],
        data: {
          activePath: FinancingDetailsSubPage.FINANCING_STRUCTURE,
        },
        resolve: [corporateFinancingStructureResolver],
      },
      {
        path: FinancingDetailsPath.MY_PARTICIPATION,
        loadComponent: () =>
          import(
            './components/participation-corporate/participation-corporate.component'
          ).then((m) => m.ParticipationCorporateComponent),
        canMatch: [participationCorporateMatchGuard],
        data: { activePath: FinancingDetailsSubPage.MY_PARTICIPATION },
      },
      {
        path: FinancingDetailsPath.MY_PARTICIPATION,
        loadComponent: () =>
          import('@fincloud/neoshare/business-case').then(
            (m) => m.SectionApplicationComponent,
          ),
        canMatch: [sectionApplicationCorporateMatchGuard],
        data: { activePath: FinancingDetailsSubPage.MY_PARTICIPATION },
      },
      {
        path: FinancingDetailsPath.MY_PARTICIPATION,
        loadComponent: () =>
          import('@fincloud/neoshare/business-case').then(
            (m) => m.SectionInvitationComponent,
          ),
        canMatch: [sectionInvitationCorporateMatchGuard],
        data: { activePath: FinancingDetailsSubPage.MY_PARTICIPATION },
      },
    ],
  },
  {
    path: '**',
    canActivate: [participationRouteGuard],
    children: [],
  },
];
