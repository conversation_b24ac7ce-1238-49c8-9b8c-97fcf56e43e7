import { Routes } from '@angular/router';
import { financingStructureActivateGuard } from '@fincloud/neoshare/business-case';
import {
  neogptChatActivateGuard,
  neogptChatDeactivateGuard,
} from '@fincloud/neoshare/neogpt-chat';
import { NeoGptActiveSession } from '@fincloud/types/enums';
import { accessLinkedCompanyGuard } from './guards/access-linked-comapny.guard';
import { loadCompanyDataRoomFolderStructuresGuard } from './guards/load-company-data-room-folder-structures.guard';
import { loadDataRoomFolderStructuresGuard } from './guards/load-data-room-folder-structures.guard';

export const ROUTES: Routes = [
  {
    path: '',
    loadComponent: () =>
      import(
        './components/business-case-data-room-tabs/business-case-data-room-tabs.component'
      ).then((m) => m.BusinessCaseDataRoomTabsComponent),
    children: [
      {
        path: '',
        redirectTo: 'case',
        pathMatch: 'full',
      },
      {
        path: 'case',
        loadComponent: () =>
          import(
            './components/business-case-data-room/business-case-data-room.component'
          ).then((m) => m.BusinessCaseDataRoomComponent),
        canActivate: [
          neogptChatActivateGuard,
          loadDataRoomFolderStructuresGuard,
          financingStructureActivateGuard,
        ],
        canDeactivate: [neogptChatDeactivateGuard],
        data: { activeSession: NeoGptActiveSession.DATA_ROOM },
      },
      {
        path: 'company',
        loadComponent: () =>
          import(
            './components/business-case-data-room-tabs/business-case-data-room-tabs.component'
          ).then((m) => m.BusinessCaseDataRoomTabsComponent),
        canActivate: [
          accessLinkedCompanyGuard,
          loadCompanyDataRoomFolderStructuresGuard,
        ],
      },
    ],
  },
  {
    path: 'inbox-document',
    loadChildren: () =>
      import('../business-case-inbox/business-case-inbox.routes').then(
        (m) => m.ROUTES,
      ),
  },
];
