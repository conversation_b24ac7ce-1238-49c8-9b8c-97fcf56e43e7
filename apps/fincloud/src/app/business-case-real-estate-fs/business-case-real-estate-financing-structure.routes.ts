import { Routes } from '@angular/router';
import { participationRouteGuard } from '@fincloud/neoshare/guards';
import {
  neogptChatActivateGuard,
  neogptChatDeactivateGuard,
} from '@fincloud/neoshare/neogpt-chat';
import {
  FinancingDetailsPath,
  FinancingDetailsSubPage,
  NeoGptActiveSession,
} from '@fincloud/types/enums';
import { financingDetailsRealEstateActivateGuard } from './guards/financing-details-real-estate-activate.guard';
import { financingStructureDeactivateGuard } from './guards/financing-structure-deactivate.guard';
import { financingStructureResolver } from './guards/financing-structure-resolver.guard';
import { initialSharedGroupsResolver } from './guards/initial-shared-groups-resolver';
import { refsResolver } from './guards/refs-resolver';
import { sectionApplicationRealEstateMatchGuard } from './guards/section-application-real-estate-match.guard';
import { sectionInvitationRealEstateMatchGuard } from './guards/section-invitation-real-estate-match.guard';
import { sharedFinancingStructureActivateGuard } from './guards/shared-financing-structure-activate.guard';

export const ROUTES: Routes = [
  {
    path: '',
    loadComponent: () =>
      import(
        './components/refs-financing-details/refs-financing-details.component'
      ).then((m) => m.RefsFinancingDetailsComponent),
    canActivate: [
      financingDetailsRealEstateActivateGuard,
      financingStructureActivateGuard,
    ],
    resolve: [refsResolver],
    children: [
      {
        path: FinancingDetailsPath.FINANCING_STRUCTURE,
        data: {
          activePath: FinancingDetailsSubPage.FINANCING_STRUCTURE,
          activeSession: NeoGptActiveSession.FINANCING_DETAILS,
        },
        loadComponent: () =>
          import('./components/refs-content/refs-content.component').then(
            (m) => m.RefsContentComponent,
          ),
        canActivate: [neogptChatActivateGuard],
        canDeactivate: [
          neogptChatDeactivateGuard,
          financingStructureDeactivateGuard,
        ],
        resolve: [financingStructureResolver],
      },
      {
        path: FinancingDetailsPath.SHARED_FINANCING_STRUCTURE,
        canActivate: [sharedFinancingStructureActivateGuard],
        canDeactivate: [financingStructureDeactivateGuard],
        resolve: [initialSharedGroupsResolver],
        data: {
          activePath: FinancingDetailsSubPage.SHARED_FINANCING_STRUCTURE,
        },
        loadComponent: () =>
          import(
            './components/refs-shared-financing-structure/refs-shared-financing-structure.component'
          ).then((m) => m.RefsSharedFinancingStructureComponent),
      },
      {
        path: FinancingDetailsPath.MY_PARTICIPATION,
        loadComponent: () =>
          import('@fincloud/neoshare/business-case').then(
            (m) => m.SectionInvitationComponent,
          ),
        data: { activePath: FinancingDetailsSubPage.MY_PARTICIPATION },
        canMatch: [sectionInvitationRealEstateMatchGuard],
      },
      {
        path: FinancingDetailsPath.MY_PARTICIPATION,
        loadComponent: () =>
          import('@fincloud/neoshare/business-case').then(
            (m) => m.SectionApplicationComponent,
          ),
        data: { activePath: FinancingDetailsSubPage.MY_PARTICIPATION },
        canMatch: [sectionApplicationRealEstateMatchGuard],
      },
    ],
  },
  {
    path: '**',
    canActivate: [participationRouteGuard],
    children: [],
  },
];
