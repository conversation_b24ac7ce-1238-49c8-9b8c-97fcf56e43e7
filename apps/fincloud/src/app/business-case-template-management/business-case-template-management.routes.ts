import { Routes } from '@angular/router';

export const ROUTES: Routes = [
  {
    path: '',
    loadComponent: () =>
      import(
        './components/business-case-template-management/business-case-template-management.component'
      ).then((m) => m.BusinessCaseTemplateManagementComponent),
    pathMatch: 'prefix',
  },
  {
    path: 'new-case-template',
    loadComponent: () =>
      import(
        './components/business-case-template-management/business-case-template-management.component'
      ).then((m) => m.BusinessCaseTemplateManagementComponent),
    pathMatch: 'prefix',
    data: { newTemplate: true, template: 'case' },
  },
  {
    path: 'new-cadr-template',
    loadComponent: () =>
      import(
        './components/business-case-template-management/business-case-template-management.component'
      ).then((m) => m.BusinessCaseTemplateManagementComponent),
    pathMatch: 'prefix',
    data: { newTemplate: true, template: 'CADR' },
  },
  {
    path: 'cadr/:id',
    loadComponent: () =>
      import(
        './components/business-case-template-management/business-case-template-management.component'
      ).then((m) => m.BusinessCaseTemplateManagementComponent),
    pathMatch: 'prefix',
  },
  {
    path: ':id',
    loadComponent: () =>
      import(
        './components/business-case-template-management/business-case-template-management.component'
      ).then((m) => m.BusinessCaseTemplateManagementComponent),
    pathMatch: 'prefix',
  },
];
