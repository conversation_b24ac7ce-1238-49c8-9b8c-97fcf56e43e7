import { Routes } from '@angular/router';
import { snapshotCopyGuard } from './guards/snapshot-copy.guard';
import { snapshotCreateGuard } from './guards/snapshot-create.guard';
import { snapshotDetailsGuard } from './guards/snapshot-details.guard';
import { SnapshotEditGuard } from './guards/snapshot-edit.guard';

export const ROUTES: Routes = [
  {
    path: '',
    loadComponent: () =>
      import('./components/demo-snapshot/demo-snapshot.component').then(
        (m) => m.DemoSnapshotComponent,
      ),
  },
  {
    path: 'create',
    loadComponent: () =>
      import(
        './components/create-demo-snapshot-stepper/create-demo-snapshot-stepper.component'
      ).then((m) => m.CreateDemoSnapshotStepperComponent),
    canActivate: [snapshotCreateGuard],
  },
  {
    path: ':uniqueIdentifier',
    loadComponent: () =>
      import(
        './components/snapshot-details-page/snapshot-details-page.component'
      ).then((m) => m.SnapshotDetailsPageComponent),
    canActivate: [snapshotDetailsGuard],
  },
  {
    path: 'copy/:uniqueIdentifier',
    loadComponent: () =>
      import(
        './components/create-demo-snapshot-stepper/create-demo-snapshot-stepper.component'
      ).then((m) => m.CreateDemoSnapshotStepperComponent),
    canActivate: [snapshotCopyGuard],
  },
  {
    path: ':uniqueIdentifier/edit',
    loadComponent: () =>
      import('./components/edit-snapshot/edit-snapshot.component').then(
        (m) => m.EditSnapshotComponent,
      ),
    canActivate: [SnapshotEditGuard],
  },
];
