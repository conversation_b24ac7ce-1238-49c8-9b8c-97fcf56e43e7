import { Routes } from '@angular/router';

export const ROUTES: Routes = [
  {
    path: '',
    loadComponent: () =>
      import(
        './components/customer-master-data/customer-master-data.component'
      ).then((m) => m.CustomerMasterDataComponent),
    children: [
      {
        path: 'details',
        loadComponent: () =>
          import(
            './components/customer-details/customer-details.component'
          ).then((m) => m.CustomerDetailsComponent),
      },
      {
        path: 'security',
        loadComponent: () =>
          import(
            './components/customer-security/customer-security.component'
          ).then((m) => m.CustomerSecurityComponent),
      },
      {
        path: 'kpi-settings',
        loadChildren: () => import('../kpis/kpis.routes').then((m) => m.ROUTES),
      },
      {
        path: 'collaboration',
        loadComponent: () =>
          import(
            './components/customer-collaboration/customer-collaboration.component'
          ).then((m) => m.CustomerCollaborationComponent),
      },
    ],
  },
];
