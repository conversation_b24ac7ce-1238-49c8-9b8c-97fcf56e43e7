import { Routes } from '@angular/router';
import { TodosStatus, TodosType } from '@fincloud/types/enums';
import { checkTodoTypeGuard } from './guards/check-todo-type.guard';
import { checkTodoStatusGuard } from './guards/check-todos-status.guard';
import { findTodoPositionGuard } from './guards/find-todo-position.guard';
import { loadBusinessCasesGuard } from './guards/load-businesscases.guard';

export const ROUTES: Routes = [
  {
    path: '',
    redirectTo: TodosType.MY_TASKS,
    pathMatch: 'full',
  },
  {
    path: ':todoType',
    canActivate: [checkTodoTypeGuard],
    children: [
      {
        path: '',
        redirectTo: TodosStatus.PENDING,
        pathMatch: 'full',
      },
      {
        path: ':todoStatus',
        canActivate: [checkTodoStatusGuard],
        children: [
          {
            path: '',
            canActivate: [loadBusinessCasesGuard],
            loadComponent: () =>
              import(
                './components/todos-management-layout/todos-management-layout.component'
              ).then((m) => m.TodosManagementLayoutComponent),
          },
          {
            path: ':businessCaseId',
            children: [
              {
                path: ':todoId',
                canActivate: [findTodoPositionGuard, loadBusinessCasesGuard],
                loadComponent: () =>
                  import(
                    './components/todos-management-layout/todos-management-layout.component'
                  ).then((m) => m.TodosManagementLayoutComponent),
              },
              {
                path: '',
                canActivate: [loadBusinessCasesGuard],
                loadComponent: () =>
                  import(
                    './components/todos-management-layout/todos-management-layout.component'
                  ).then((m) => m.TodosManagementLayoutComponent),
              },
            ],
          },
        ],
      },
    ],
  },
];
