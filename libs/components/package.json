{"name": "@fincloud/components", "peerDependencies": {"@ag-grid-community/angular": "30.1.0", "@ag-grid-community/core": "30.1.0", "@angular/common": "20.3.3", "@angular/core": "20.3.3", "@angular/animations": "20.3.3", "@fincloud/state": "0.0.1", "@fincloud/core": "0.0.1", "@fincloud/types": "0.0.1", "@angular/cdk": "20.2.7", "@ngrx/store": "20.0.1", "rxjs": "7.8.1", "@angular/router": "20.3.3", "azure-maps-control": "3.3.0", "lodash-es": "4.17.21", "azure-maps-rest": "2.1.1", "@angular/forms": "20.3.3", "ngx-scrollbar": "16.1.0", "@ng-bootstrap/ng-bootstrap": "19.0.1", "@angular/platform-browser": "20.3.3", "@swimlane/ngx-charts": "20.5.0", "ng-circle-progress": "1.7.1", "ngx-echarts": "17.2.0", "echarts": "5.5.0", "@fincloud/swagger-generator": "0.0.1", "ngx-ui-tour-ngx-bootstrap": "15.0.1", "hot-formula-parser": "4.0.0", "dayjs": "1.11.11", "@ngx-formly/core": "6.3.3", "angular-cropperjs": "14.0.1", "ngx-infinite-scroll": "20.0.0", "@angular-slider/ngx-slider": "20.0.0", "@siemens/ngx-datatable": "22.4.1", "ngx-spinner": "17.0.0", "ngx-extended-pdf-viewer": "25.5.1", "@ng-select/ng-select": "15.1.3", "@fincloud/ui": "0.7.1-next.49", "@fincloud/utils": "0.7.1-next.49", "@fincloud/ngx-currency": "20.0.0", "@fincloud/ngx-drag-scroll": "20.0.1"}, "sideEffects": false, "version": "0.0.1"}