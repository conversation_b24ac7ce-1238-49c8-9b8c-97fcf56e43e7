import { constantCase } from 'change-case';
import { writeFileSync } from 'fs';
import { glob } from 'glob';
import { basename, dirname, join } from 'path';
import { Project, SyntaxKind } from 'ts-morph';

// Create a project
const project = new Project({
  tsConfigFilePath: 'tsconfig.base.json',
  skipAddingFilesFromTsConfig: true,
});

// Step 1: Find all routing module files in apps/fincloud/src/app/ directory
const routingModuleFiles = glob.sync(['apps/fincloud/src/app/**/*.module.ts']);

console.log(`Found ${routingModuleFiles.length} routing module files`);

// Step 2: Process each routing module file
let processedFiles = 0;
let createdFiles = 0;
let modifiedFiles = 0;

for (const routingModuleFile of routingModuleFiles) {
  try {
    console.log(`Processing: ${routingModuleFile}`);

    const sourceFile = project.addSourceFileAtPath(routingModuleFile);
    let routesConstant = null;
    let routesConstantName = 'routes';
    let fileModified = false;

    // Find the routes constant declaration
    const variableStatements = sourceFile.getVariableStatements();

    for (const variableStatement of variableStatements) {
      const declarations = variableStatement.getDeclarations();

      for (const declaration of declarations) {
        const name = declaration.getName();
        const typeNode = declaration.getTypeNode();

        // Check if this is a Routes constant
        if (typeNode && typeNode.getText() === 'Routes') {
          routesConstant = variableStatement;
          routesConstantName = name;
          break;
        }
      }

      if (routesConstant) break;
    }

    if (routesConstant) {
      // Extract the constant text
      const constantText = routesConstant.getFullText();
      const importsDeclarations = sourceFile.getImportDeclarations();

      let importDeclarationText = '';

      importsDeclarations.forEach((importDecl) => {
        importDeclarationText += importDecl.getText() + '\n';
      });

      // Get the initializer (the array part)
      const declaration = routesConstant.getDeclarations()[0];
      const initializer = declaration.getInitializer();

      if (initializer) {
        // Generate the new file name
        const dir = dirname(routingModuleFile);
        const fileName = basename(routingModuleFile, '.module.ts');
        const newFileName = `${fileName.replace('-routing', '')}.routes.ts`;
        const newFilePath = join(dir, newFileName);

        // Generate the constant name in CONSTANT_CASE
        const constantName = constantCase(routesConstantName);

        // Create the new file content
        const newFileContent = `
        ${importDeclarationText}
        import { Routes } from '@angular/router';

export const ${constantName}: Routes = ${initializer.getText()};
`;

        // Write the new file
        writeFileSync(newFilePath, newFileContent);
        createdFiles++;
        console.log(`  Created: ${newFilePath}`);

        // Remove the constant from the original file
        routesConstant.remove();
        fileModified = true;

        // Check if we need to remove the Routes import if it's no longer used
        const importsDeclarations = sourceFile.getImportDeclarations();
        for (const importDecl of importsDeclarations) {
          const moduleSpecifier = importDecl.getModuleSpecifierValue();
          if (moduleSpecifier === '@angular/router') {
            const namedImports = importDecl.getNamedImports();
            const routesImport = namedImports.find(
              (imp) => imp.getName() === 'Routes',
            );

            if (routesImport) {
              // Check if Routes is still used in the file
              const routesUsages = sourceFile
                .getDescendantsOfKind(SyntaxKind.Identifier)
                .filter(
                  (id) =>
                    id.getText() === 'Routes' &&
                    id !== routesImport.getNameNode(),
                );

              if (routesUsages.length === 0) {
                // Remove the Routes import
                if (namedImports.length === 1) {
                  // If Routes is the only import, remove the entire import declaration
                  importDecl.remove();
                } else {
                  // Remove just the Routes import
                  routesImport.remove();
                }
                console.log(
                  `  Removed unused Routes import from: ${routingModuleFile}`,
                );
              }
            }
          }
        }
      }
    } else {
      console.log(`  No routes constant found in: ${routingModuleFile}`);
    }

    // Save the modified source file
    if (fileModified) {
      sourceFile.saveSync();
      modifiedFiles++;
      console.log(`  Modified: ${routingModuleFile}`);
    }

    processedFiles++;
  } catch (error) {
    console.error(`Error processing ${routingModuleFile}:`, error.message);
  }
}

// Output summary
console.log('\n=== Summary ===');
console.log(`Processed files: ${processedFiles}`);
console.log(`Created route files: ${createdFiles}`);
console.log(`Modified routing modules: ${modifiedFiles}`);
