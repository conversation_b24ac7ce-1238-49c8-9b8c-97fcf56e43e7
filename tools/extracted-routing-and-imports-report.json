{"summary": {"processedFiles": 68, "createdFiles": 37, "modifiedFiles": 37, "totalImports": 376, "filesWithRoutes": 37, "filesWithErrors": 0}, "files": [{"filePath": "apps/fincloud/src/app/voluntary-payment/voluntary-payment.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: '',\n    loadComponent: () => import('./components/billing-management/billing-management.component').then(m => m.BillingManagementComponent),\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./components/billing-management/billing-management.component').then(m => m.BillingManagementComponent),\n  },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/user-signing/user-signing.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/common/http", "defaultImport": null, "namedImports": [{"name": "provideHttpClient", "alias": null}, {"name": "withInterceptorsFromDi", "alias": null}], "namespaceImport": null, "fullText": "import {\n  provideHttpClient,\n  withInterceptorsFromDi,\n} from '@angular/common/http';"}, {"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "./user-signing-routing.module", "defaultImport": null, "namedImports": [{"name": "UserSigningRoutingModule", "alias": null}], "namespaceImport": null, "fullText": "import { UserSigningRoutingModule } from './user-signing-routing.module';"}], "hasRoutesConstant": false, "routesConstant": null, "error": null}, {"filePath": "apps/fincloud/src/app/user-signing/user-signing-routing.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}, {"type": "named", "moduleSpecifier": "./guards/user-signing-redirect.guard", "defaultImport": null, "namedImports": [{"name": "userSigningRedirectGuard", "alias": null}], "namespaceImport": null, "fullText": "import { userSigningRedirectGuard } from './guards/user-signing-redirect.guard';"}, {"type": "named", "moduleSpecifier": "./guards/user-signing.guard", "defaultImport": null, "namedImports": [{"name": "userSigningGuard", "alias": null}], "namespaceImport": null, "fullText": "import { userSigningGuard } from './guards/user-signing.guard';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: ':contractId',\n    canActivate: [userSigningGuard],\n    loadComponent: () => import('./components/user-signing-main-page/user-signing-main-page.component').then(m => m.UserSigningMainPageComponent),\n    children: [\n      {\n        path: 'redirect/:code',\n        loadComponent: () => import('./components/user-signing-info/user-signing-info.component').then(m => m.UserSigningInfoComponent),\n        canActivate: [userSigningRedirectGuard],\n      },\n      {\n        path: 'success',\n        loadComponent: () => import('./components/user-signing-success/user-signing-success.component').then(m => m.UserSigningSuccessComponent),\n      },\n      {\n        path: 'success/:code',\n        loadComponent: () => import('./components/user-signing-success/user-signing-success.component').then(m => m.UserSigningSuccessComponent),\n      },\n      {\n        path: 'reject',\n        loadComponent: () => import('./components/user-signing-reject/user-signing-reject.component').then(m => m.UserSigningRejectDocumentComponent),\n      },\n      {\n        path: 'session-expired/:code',\n        loadComponent: () => import('./components/user-signing-session-expired/user-signing-session-expired.component').then(m => m.UserSigningSessionExpiredComponent),\n      },\n      {\n        path: 'error/:code',\n        loadComponent: () => import('./components/user-signing-error/user-signing-error.component').then(m => m.UserSigningErrorComponent),\n      },\n      {\n        path: 'voided',\n        loadComponent: () => import('./components/user-signing-voided/user-signing-voided.component').then(m => m.UserSigningVoidedComponent),\n      },\n      {\n        path: 'sign-later/:code',\n        loadComponent: () => import('./components/user-signing-sign-later/user-signing-sign-later.component').then(m => m.UserSigningSignLaterComponent),\n      },\n      {\n        path: 'access_code_failed/:code',\n        loadComponent: () => import('./components/user-signing-auth-code-failed/user-signing-auth-code-failed.component').then(m => m.UserSigningAuthCodeFailedComponent),\n      },\n      {\n        path: ':code',\n        loadComponent: () => import('./components/user-signing-info/user-signing-info.component').then(m => m.UserSigningInfoComponent),\n      },\n    ],\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: ':contractId',\n    canActivate: [userSigningGuard],\n    loadComponent: () => import('./components/user-signing-main-page/user-signing-main-page.component').then(m => m.UserSigningMainPageComponent),\n    children: [\n      {\n        path: 'redirect/:code',\n        loadComponent: () => import('./components/user-signing-info/user-signing-info.component').then(m => m.UserSigningInfoComponent),\n        canActivate: [userSigningRedirectGuard],\n      },\n      {\n        path: 'success',\n        loadComponent: () => import('./components/user-signing-success/user-signing-success.component').then(m => m.UserSigningSuccessComponent),\n      },\n      {\n        path: 'success/:code',\n        loadComponent: () => import('./components/user-signing-success/user-signing-success.component').then(m => m.UserSigningSuccessComponent),\n      },\n      {\n        path: 'reject',\n        loadComponent: () => import('./components/user-signing-reject/user-signing-reject.component').then(m => m.UserSigningRejectDocumentComponent),\n      },\n      {\n        path: 'session-expired/:code',\n        loadComponent: () => import('./components/user-signing-session-expired/user-signing-session-expired.component').then(m => m.UserSigningSessionExpiredComponent),\n      },\n      {\n        path: 'error/:code',\n        loadComponent: () => import('./components/user-signing-error/user-signing-error.component').then(m => m.UserSigningErrorComponent),\n      },\n      {\n        path: 'voided',\n        loadComponent: () => import('./components/user-signing-voided/user-signing-voided.component').then(m => m.UserSigningVoidedComponent),\n      },\n      {\n        path: 'sign-later/:code',\n        loadComponent: () => import('./components/user-signing-sign-later/user-signing-sign-later.component').then(m => m.UserSigningSignLaterComponent),\n      },\n      {\n        path: 'access_code_failed/:code',\n        loadComponent: () => import('./components/user-signing-auth-code-failed/user-signing-auth-code-failed.component').then(m => m.UserSigningAuthCodeFailedComponent),\n      },\n      {\n        path: ':code',\n        loadComponent: () => import('./components/user-signing-info/user-signing-info.component').then(m => m.UserSigningInfoComponent),\n      },\n    ],\n  },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/user-settings/user-settings.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}, {"type": "named", "moduleSpecifier": "./services/user-settings-helper.service", "defaultImport": null, "namedImports": [{"name": "UserSettingsHelperService", "alias": null}], "namespaceImport": null, "fullText": "import { UserSettingsHelperService } from './services/user-settings-helper.service';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: '',\n    loadComponent: () => import('./components/user-settings/user-settings.component').then(m => m.UserSettingsComponent),\n    children: [\n      {\n        path: '',\n        redirectTo: 'personal-data',\n        pathMatch: 'full',\n      },\n      {\n        path: 'personal-data',\n        loadComponent: () => import('./components/user-settings-personal-data-tab/user-settings-personal-data-tab.component').then(m => m.UserSettingsPersonalDataTabComponent),\n      },\n      {\n        path: 'account-security',\n        loadComponent: () => import('./components/user-settings-account-security-tab/user-settings-account-security-tab.component').then(m => m.UserSettingsAccountSecurityTabComponent),\n      },\n      {\n        path: 'preferences',\n        loadChildren: () =>\n          import('../user-preferences/user-preferences.module').then(\n            (m) => m.UserPreferencesModule,\n          ),\n      },\n    ],\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./components/user-settings/user-settings.component').then(m => m.UserSettingsComponent),\n    children: [\n      {\n        path: '',\n        redirectTo: 'personal-data',\n        pathMatch: 'full',\n      },\n      {\n        path: 'personal-data',\n        loadComponent: () => import('./components/user-settings-personal-data-tab/user-settings-personal-data-tab.component').then(m => m.UserSettingsPersonalDataTabComponent),\n      },\n      {\n        path: 'account-security',\n        loadComponent: () => import('./components/user-settings-account-security-tab/user-settings-account-security-tab.component').then(m => m.UserSettingsAccountSecurityTabComponent),\n      },\n      {\n        path: 'preferences',\n        loadChildren: () =>\n          import('../user-preferences/user-preferences.module').then(\n            (m) => m.UserPreferencesModule,\n          ),\n      },\n    ],\n  },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/user-preferences/user-preferences.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}, {"type": "named", "moduleSpecifier": "@fincloud/state/user", "defaultImport": null, "namedImports": [{"name": "StateLibUserPreferencesEffects", "alias": null}], "namespaceImport": null, "fullText": "import { StateLibUserPreferencesEffects } from '@fincloud/state/user';"}, {"type": "named", "moduleSpecifier": "@ngrx/effects", "defaultImport": null, "namedImports": [{"name": "provideEffects", "alias": null}], "namespaceImport": null, "fullText": "import { provideEffects } from '@ngrx/effects';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: '',\n    loadComponent: () => import('./components/user-settings-preferences-tab/user-settings-preferences-tab.component').then(m => m.UserSettingsPreferencesTabComponent),\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./components/user-settings-preferences-tab/user-settings-preferences-tab.component').then(m => m.UserSettingsPreferencesTabComponent),\n  },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/user-management/user-management.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: '',\n    loadComponent: () => import('./components/user-management/user-management.component').then(m => m.UserManagementComponent),\n    children: [\n      {\n        loadComponent: () => import('./components/user-management/user-management.component').then(m => m.UserManagementComponent),\n        path: ':userId',\n      },\n      {\n        loadComponent: () => import('./components/user-management/user-management.component').then(m => m.UserManagementComponent),\n        path: 'create-user',\n      },\n    ],\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./components/user-management/user-management.component').then(m => m.UserManagementComponent),\n    children: [\n      {\n        loadComponent: () => import('./components/user-management/user-management.component').then(m => m.UserManagementComponent),\n        path: ':userId',\n      },\n      {\n        loadComponent: () => import('./components/user-management/user-management.component').then(m => m.UserManagementComponent),\n        path: 'create-user',\n      },\n    ],\n  },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/todos-management/todos-management.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@ngrx/effects", "defaultImport": null, "namedImports": [{"name": "provideEffects", "alias": null}], "namespaceImport": null, "fullText": "import { provideEffects } from '@ngrx/effects';"}, {"type": "named", "moduleSpecifier": "@ngrx/store", "defaultImport": null, "namedImports": [{"name": "provideState", "alias": null}], "namespaceImport": null, "fullText": "import { provideState } from '@ngrx/store';"}, {"type": "named", "moduleSpecifier": "./+state/effects/todos-management.effects", "defaultImport": null, "namedImports": [{"name": "TodosManagementEffects", "alias": null}], "namespaceImport": null, "fullText": "import { TodosManagementEffects } from './+state/effects/todos-management.effects';"}, {"type": "named", "moduleSpecifier": "./+state/reducers/todos-management.reducer", "defaultImport": null, "namedImports": [{"name": "todosManagementFeature", "alias": null}], "namespaceImport": null, "fullText": "import { todosManagementFeature } from './+state/reducers/todos-management.reducer';"}, {"type": "named", "moduleSpecifier": "./todos-management-routing.module", "defaultImport": null, "namedImports": [{"name": "TodosManagementRoutingModule", "alias": null}], "namespaceImport": null, "fullText": "import { TodosManagementRoutingModule } from './todos-management-routing.module';"}], "hasRoutesConstant": false, "routesConstant": null, "error": null}, {"filePath": "apps/fincloud/src/app/todos-management/todos-management-routing.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}, {"type": "named", "moduleSpecifier": "@fincloud/types/enums", "defaultImport": null, "namedImports": [{"name": "TodosStatus", "alias": null}, {"name": "TodosType", "alias": null}], "namespaceImport": null, "fullText": "import { TodosStatus, TodosType } from '@fincloud/types/enums';"}, {"type": "named", "moduleSpecifier": "./guards/check-todo-type.guard", "defaultImport": null, "namedImports": [{"name": "checkTodoTypeGuard", "alias": null}], "namespaceImport": null, "fullText": "import { checkTodoTypeGuard } from './guards/check-todo-type.guard';"}, {"type": "named", "moduleSpecifier": "./guards/check-todos-status.guard", "defaultImport": null, "namedImports": [{"name": "checkTodoStatusGuard", "alias": null}], "namespaceImport": null, "fullText": "import { checkTodoStatusGuard } from './guards/check-todos-status.guard';"}, {"type": "named", "moduleSpecifier": "./guards/find-todo-position.guard", "defaultImport": null, "namedImports": [{"name": "findTodoPositionGuard", "alias": null}], "namespaceImport": null, "fullText": "import { findTodoPositionGuard } from './guards/find-todo-position.guard';"}, {"type": "named", "moduleSpecifier": "./guards/load-businesscases.guard", "defaultImport": null, "namedImports": [{"name": "loadBusinessCasesGuard", "alias": null}], "namespaceImport": null, "fullText": "import { loadBusinessCasesGuard } from './guards/load-businesscases.guard';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: '',\n    redirectTo: TodosType.MY_TASKS,\n    pathMatch: 'full',\n  },\n  {\n    path: ':todoType',\n    canActivate: [checkTodoTypeGuard],\n    children: [\n      {\n        path: '',\n        redirectTo: TodosStatus.PENDING,\n        pathMatch: 'full',\n      },\n      {\n        path: ':todoStatus',\n        canActivate: [checkTodoStatusGuard],\n        children: [\n          {\n            path: '',\n            canActivate: [loadBusinessCasesGuard],\n            loadComponent: () => import('./components/todos-management-layout/todos-management-layout.component').then(m => m.TodosManagementLayoutComponent),\n          },\n          {\n            path: ':businessCaseId',\n            children: [\n              {\n                path: ':todoId',\n                canActivate: [findTodoPositionGuard, loadBusinessCasesGuard],\n                loadComponent: () => import('./components/todos-management-layout/todos-management-layout.component').then(m => m.TodosManagementLayoutComponent),\n              },\n              {\n                path: '',\n                canActivate: [loadBusinessCasesGuard],\n                loadComponent: () => import('./components/todos-management-layout/todos-management-layout.component').then(m => m.TodosManagementLayoutComponent),\n              },\n            ],\n          },\n        ],\n      },\n    ],\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: '',\n    redirectTo: TodosType.MY_TASKS,\n    pathMatch: 'full',\n  },\n  {\n    path: ':todoType',\n    canActivate: [checkTodoTypeGuard],\n    children: [\n      {\n        path: '',\n        redirectTo: TodosStatus.PENDING,\n        pathMatch: 'full',\n      },\n      {\n        path: ':todoStatus',\n        canActivate: [checkTodoStatusGuard],\n        children: [\n          {\n            path: '',\n            canActivate: [loadBusinessCasesGuard],\n            loadComponent: () => import('./components/todos-management-layout/todos-management-layout.component').then(m => m.TodosManagementLayoutComponent),\n          },\n          {\n            path: ':businessCaseId',\n            children: [\n              {\n                path: ':todoId',\n                canActivate: [findTodoPositionGuard, loadBusinessCasesGuard],\n                loadComponent: () => import('./components/todos-management-layout/todos-management-layout.component').then(m => m.TodosManagementLayoutComponent),\n              },\n              {\n                path: '',\n                canActivate: [loadBusinessCasesGuard],\n                loadComponent: () => import('./components/todos-management-layout/todos-management-layout.component').then(m => m.TodosManagementLayoutComponent),\n              },\n            ],\n          },\n        ],\n      },\n    ],\n  },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/terms-and-conditions/terms-and-conditions.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "./terms-and-conditions-routing.module", "defaultImport": null, "namedImports": [{"name": "TermsAndConditionsRoutingModule", "alias": null}], "namespaceImport": null, "fullText": "import { TermsAndConditionsRoutingModule } from './terms-and-conditions-routing.module';"}], "hasRoutesConstant": false, "routesConstant": null, "error": null}, {"filePath": "apps/fincloud/src/app/terms-and-conditions/terms-and-conditions-routing.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: '',\n    loadComponent: () => import('@fincloud/components/navigation').then(m => m.BaseLayoutComponent),\n    children: [\n      {\n        path: '',\n        loadComponent: () => import('./components/accept-terms/accept-terms.component').then(m => m.AcceptTermsComponent),\n      },\n    ],\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('@fincloud/components/navigation').then(m => m.BaseLayoutComponent),\n    children: [\n      {\n        path: '',\n        loadComponent: () => import('./components/accept-terms/accept-terms.component').then(m => m.AcceptTermsComponent),\n      },\n    ],\n  },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/support/support.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "./support-routing.module", "defaultImport": null, "namedImports": [{"name": "SupportRoutingModule", "alias": null}], "namespaceImport": null, "fullText": "import { SupportRoutingModule } from './support-routing.module';"}], "hasRoutesConstant": false, "routesConstant": null, "error": null}, {"filePath": "apps/fincloud/src/app/support/support-routing.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: '',\n    loadComponent: () => import('@fincloud/components/navigation').then(m => m.BaseLayoutComponent),\n    children: [\n      {\n        path: 'cancelled',\n        loadComponent: () => import('./components/cancelled-invitation/cancelled-invitation.component').then(m => m.CancelledInvitationComponent),\n      },\n      {\n        path: 'support',\n        loadComponent: () => import('./components/contact-support/contact-support.component').then(m => m.ContactSupportComponent),\n      },\n    ],\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('@fincloud/components/navigation').then(m => m.BaseLayoutComponent),\n    children: [\n      {\n        path: 'cancelled',\n        loadComponent: () => import('./components/cancelled-invitation/cancelled-invitation.component').then(m => m.CancelledInvitationComponent),\n      },\n      {\n        path: 'support',\n        loadComponent: () => import('./components/contact-support/contact-support.component').then(m => m.ContactSupportComponent),\n      },\n    ],\n  },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/login/login.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@ngrx/effects", "defaultImport": null, "namedImports": [{"name": "provideEffects", "alias": null}], "namespaceImport": null, "fullText": "import { provideEffects } from '@ngrx/effects';"}, {"type": "named", "moduleSpecifier": "@ngrx/store", "defaultImport": null, "namedImports": [{"name": "provideState", "alias": null}], "namespaceImport": null, "fullText": "import { provideState } from '@ngrx/store';"}, {"type": "named", "moduleSpecifier": "./login-routing.module", "defaultImport": null, "namedImports": [{"name": "LoginRoutingModule", "alias": null}], "namespaceImport": null, "fullText": "import { LoginRoutingModule } from './login-routing.module';"}, {"type": "named", "moduleSpecifier": "@fincloud/state/metareducers", "defaultImport": null, "namedImports": [{"name": "clearLoginState", "alias": null}], "namespaceImport": null, "fullText": "import { clearLoginState } from '@fincloud/state/metareducers';"}, {"type": "named", "moduleSpecifier": "./+state/login.effects", "defaultImport": null, "namedImports": [{"name": "LoginEffects", "alias": null}], "namespaceImport": null, "fullText": "import { LoginEffects } from './+state/login.effects';"}, {"type": "named", "moduleSpecifier": "./+state/login.reducer", "defaultImport": null, "namedImports": [{"name": "initialState", "alias": null}, {"name": "loginReducer", "alias": null}, {"name": "loginStateSlice", "alias": null}], "namespaceImport": null, "fullText": "import {\n  initialState,\n  loginReducer,\n  loginStateSlice,\n} from './+state/login.reducer';"}], "hasRoutesConstant": false, "routesConstant": null, "error": null}, {"filePath": "apps/fincloud/src/app/login/login-routing.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}, {"type": "named", "moduleSpecifier": "./guards/check-device-cookie.guard", "defaultImport": null, "namedImports": [{"name": "checkDevice<PERSON><PERSON><PERSON><PERSON><PERSON>", "alias": null}], "namespaceImport": null, "fullText": "import { checkDeviceCookieG<PERSON> } from './guards/check-device-cookie.guard';"}, {"type": "named", "moduleSpecifier": "./guards/confirm-device.guard", "defaultImport": null, "namedImports": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alias": null}], "namespaceImport": null, "fullText": "import { confirmDeviceGuard } from './guards/confirm-device.guard';"}, {"type": "named", "moduleSpecifier": "./guards/device-authentication-cookie-check.guard", "defaultImport": null, "namedImports": [{"name": "deviceAuthenticationCookieCheckGuard", "alias": null}], "namespaceImport": null, "fullText": "import { deviceAuthenticationCookieCheckGuard } from './guards/device-authentication-cookie-check.guard';"}, {"type": "named", "moduleSpecifier": "./guards/device-authentication.guard", "defaultImport": null, "namedImports": [{"name": "deviceAuthenticationGuard", "alias": null}], "namespaceImport": null, "fullText": "import { deviceAuthenticationGuard } from './guards/device-authentication.guard';"}, {"type": "named", "moduleSpecifier": "./guards/different-device.guard", "defaultImport": null, "namedImports": [{"name": "differentDevice<PERSON><PERSON>", "alias": null}], "namespaceImport": null, "fullText": "import { differentDeviceGuard } from './guards/different-device.guard';"}, {"type": "named", "moduleSpecifier": "./guards/email-authentication.guard", "defaultImport": null, "namedImports": [{"name": "emailAuthenticationGuard", "alias": null}], "namespaceImport": null, "fullText": "import { emailAuthenticationGuard } from './guards/email-authentication.guard';"}, {"type": "named", "moduleSpecifier": "./guards/forgot-password.guard", "defaultImport": null, "namedImports": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alias": null}], "namespaceImport": null, "fullText": "import { forgotPasswordGuard } from './guards/forgot-password.guard';"}, {"type": "named", "moduleSpecifier": "./guards/get-password-page-data.guard", "defaultImport": null, "namedImports": [{"name": "getPasswordPageData", "alias": null}], "namespaceImport": null, "fullText": "import { getPasswordPageData } from './guards/get-password-page-data.guard';"}, {"type": "named", "moduleSpecifier": "./guards/load-customer-keys.guard", "defaultImport": null, "namedImports": [{"name": "loadCustomerKeysGuard", "alias": null}], "namespaceImport": null, "fullText": "import { loadCustomerKeysGuard } from './guards/load-customer-keys.guard';"}, {"type": "named", "moduleSpecifier": "./guards/load-customer.guard", "defaultImport": null, "namedImports": [{"name": "loadCustomerGuard", "alias": null}], "namespaceImport": null, "fullText": "import { loadCustomerGuard } from './guards/load-customer.guard';"}, {"type": "named", "moduleSpecifier": "./guards/lock-user.guard", "defaultImport": null, "namedImports": [{"name": "lockUserGuard", "alias": null}], "namespaceImport": null, "fullText": "import { lockUserGuard } from './guards/lock-user.guard';"}, {"type": "named", "moduleSpecifier": "./guards/login-success.guard", "defaultImport": null, "namedImports": [{"name": "loginSuccessGuard", "alias": null}], "namespaceImport": null, "fullText": "import { loginSuccessGuard } from './guards/login-success.guard';"}, {"type": "named", "moduleSpecifier": "./guards/password-type.guard", "defaultImport": null, "namedImports": [{"name": "passwordTypeGuard", "alias": null}], "namespaceImport": null, "fullText": "import { passwordTypeGuard } from './guards/password-type.guard';"}, {"type": "named", "moduleSpecifier": "./guards/redirection-error.guard", "defaultImport": null, "namedImports": [{"name": "redirectionErrorGuard", "alias": null}], "namespaceImport": null, "fullText": "import { redirectionErrorGuard } from './guards/redirection-error.guard';"}, {"type": "named", "moduleSpecifier": "./guards/select-organization.guard", "defaultImport": null, "namedImports": [{"name": "selectOrganizationGuard", "alias": null}], "namespaceImport": null, "fullText": "import { selectOrganizationGuard } from './guards/select-organization.guard';"}, {"type": "named", "moduleSpecifier": "./guards/unlock-user.guard", "defaultImport": null, "namedImports": [{"name": "unlockUserGuard", "alias": null}], "namespaceImport": null, "fullText": "import { unlockUserGuard } from './guards/unlock-user.guard';"}, {"type": "named", "moduleSpecifier": "./guards/user-authentication.guard", "defaultImport": null, "namedImports": [{"name": "userAuthenticationGuard", "alias": null}], "namespaceImport": null, "fullText": "import { userAuthenticationGuard } from './guards/user-authentication.guard';"}, {"type": "named", "moduleSpecifier": "./guards/user-error.guard", "defaultImport": null, "namedImports": [{"name": "userErrorGuard", "alias": null}], "namespaceImport": null, "fullText": "import { userErrorGuard } from './guards/user-error.guard';"}, {"type": "named", "moduleSpecifier": "./guards/user-validation.guard", "defaultImport": null, "namedImports": [{"name": "userValidationGuard", "alias": null}], "namespaceImport": null, "fullText": "import { userValidationGuard } from './guards/user-validation.guard';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: '',\n    loadComponent: () => import('@fincloud/neoshare/auth').then(m => m.LoginComponent),\n    canActivate: [unlockUserGuard],\n    children: [\n      {\n        path: 'email-authentication',\n        loadComponent: () => import('./components/email-authentication/email-authentication.component').then(m => m.EmailAuthenticationComponent),\n        canActivate: [emailAuthenticationGuard],\n      },\n      {\n        path: 'device-authentication',\n        loadComponent: () => import('./components/device-authentication/device-authentication.component').then(m => m.DeviceAuthenticationComponent),\n        canActivate: [\n          confirmDeviceGuard, // Confirm device logic from email link\n          deviceAuthenticationGuard,\n          deviceAuthenticationCookieCheckGuard,\n        ],\n      },\n      {\n        path: 'select-organization',\n        loadComponent: () => import('./components/select-organization/select-organization.component').then(m => m.SelectOrganizationComponent),\n        canActivate: [\n          checkDeviceCookieGuard,\n          loadCustomerKeysGuard,\n          selectOrganizationGuard,\n          loadCustomerGuard,\n        ],\n      },\n      {\n        path: 'user-authentication',\n        loadComponent: () => import('./components/user-authentication/user-authentication.component').then(m => m.UserAuthenticationComponent),\n        canActivate: [\n          checkDeviceCookieGuard,\n          lockUserGuard,\n          userAuthenticationGuard,\n        ],\n      },\n      {\n        path: 'user-validation',\n        loadComponent: () => import('./components/user-validation/user-validation.component').then(m => m.UserValidationComponent),\n        canActivate: [\n          redirectionErrorGuard,\n          userValidationGuard,\n          loginSuccessGuard,\n          loadCustomerGuard,\n        ],\n      },\n      {\n        path: 'reset-password/:resetPasswordType',\n        canActivate: [\n          differentDeviceGuard,\n          passwordTypeGuard,\n          forgotPasswordGuard,\n          getPasswordPageData,\n        ],\n        loadComponent: () => import('./components/reset-password/reset-password.component').then(m => m.ResetPasswordComponent),\n      },\n      {\n        path: 'fail',\n        loadComponent: () => import('./components/user-error/user-error.component').then(m => m.UserErrorComponent),\n        canActivate: [userErrorGuard],\n      },\n      {\n        path: '**',\n        redirectTo: 'email-authentication',\n      },\n    ],\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('@fincloud/neoshare/auth').then(m => m.LoginComponent),\n    canActivate: [unlockUserGuard],\n    children: [\n      {\n        path: 'email-authentication',\n        loadComponent: () => import('./components/email-authentication/email-authentication.component').then(m => m.EmailAuthenticationComponent),\n        canActivate: [emailAuthenticationGuard],\n      },\n      {\n        path: 'device-authentication',\n        loadComponent: () => import('./components/device-authentication/device-authentication.component').then(m => m.DeviceAuthenticationComponent),\n        canActivate: [\n          confirmDeviceGuard, // Confirm device logic from email link\n          deviceAuthenticationGuard,\n          deviceAuthenticationCookieCheckGuard,\n        ],\n      },\n      {\n        path: 'select-organization',\n        loadComponent: () => import('./components/select-organization/select-organization.component').then(m => m.SelectOrganizationComponent),\n        canActivate: [\n          checkDeviceCookieGuard,\n          loadCustomerKeysGuard,\n          selectOrganizationGuard,\n          loadCustomerGuard,\n        ],\n      },\n      {\n        path: 'user-authentication',\n        loadComponent: () => import('./components/user-authentication/user-authentication.component').then(m => m.UserAuthenticationComponent),\n        canActivate: [\n          checkDeviceCookieGuard,\n          lockUserGuard,\n          userAuthenticationGuard,\n        ],\n      },\n      {\n        path: 'user-validation',\n        loadComponent: () => import('./components/user-validation/user-validation.component').then(m => m.UserValidationComponent),\n        canActivate: [\n          redirectionErrorGuard,\n          userValidationGuard,\n          loginSuccessGuard,\n          loadCustomerGuard,\n        ],\n      },\n      {\n        path: 'reset-password/:resetPasswordType',\n        canActivate: [\n          differentDeviceGuard,\n          passwordTypeGuard,\n          forgotPasswordGuard,\n          getPasswordPageData,\n        ],\n        loadComponent: () => import('./components/reset-password/reset-password.component').then(m => m.ResetPasswordComponent),\n      },\n      {\n        path: 'fail',\n        loadComponent: () => import('./components/user-error/user-error.component').then(m => m.UserErrorComponent),\n        canActivate: [userErrorGuard],\n      },\n      {\n        path: '**',\n        redirectTo: 'email-authentication',\n      },\n    ],\n  },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/layout/layout.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}], "hasRoutesConstant": false, "routesConstant": null, "error": null}, {"filePath": "apps/fincloud/src/app/kpis/kpis.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@fincloud/state/utils", "defaultImport": null, "namedImports": [{"name": "KPIS_FEATURE_KEY", "alias": null}], "namespaceImport": null, "fullText": "import { KPIS_FEATURE_KEY } from '@fincloud/state/utils';"}, {"type": "named", "moduleSpecifier": "@ngrx/effects", "defaultImport": null, "namedImports": [{"name": "provideEffects", "alias": null}], "namespaceImport": null, "fullText": "import { provideEffects } from '@ngrx/effects';"}, {"type": "named", "moduleSpecifier": "@ngrx/store", "defaultImport": null, "namedImports": [{"name": "provideState", "alias": null}], "namespaceImport": null, "fullText": "import { provideState } from '@ngrx/store';"}, {"type": "namespace", "moduleSpecifier": "./+state", "defaultImport": null, "namedImports": [], "namespaceImport": "effects", "fullText": "import * as effects from './+state';"}, {"type": "named", "moduleSpecifier": "./+state/reducers/kpi.reducer", "defaultImport": null, "namedImports": [{"name": "stateLibKpiStateReducer", "alias": null}], "namespaceImport": null, "fullText": "import { stateLibKpiStateReducer } from './+state/reducers/kpi.reducer';"}, {"type": "named", "moduleSpecifier": "./kpis-routing.module", "defaultImport": null, "namedImports": [{"name": "KpisRoutingModule", "alias": null}], "namespaceImport": null, "fullText": "import { KpisRoutingModule } from './kpis-routing.module';"}], "hasRoutesConstant": false, "routesConstant": null, "error": null}, {"filePath": "apps/fincloud/src/app/kpis/kpis-routing.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}, {"type": "named", "moduleSpecifier": "./guards/kpis-list.guard", "defaultImport": null, "namedImports": [{"name": "kpis<PERSON>ist<PERSON><PERSON>", "alias": null}], "namespaceImport": null, "fullText": "import { kpisListGuard } from './guards/kpis-list.guard';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: '',\n    loadComponent: () => import('./components/customer-kpi-settings/customer-kpi-settings.component').then(m => m.CustomerKpiSettingsComponent),\n    canActivate: [kpisListGuard],\n    children: [\n      {\n        path: ':kpiKey',\n        loadComponent: () => import('./components/customer-kpi-list/customer-kpi-list.component').then(m => m.CustomerKpiListComponent),\n      },\n    ],\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./components/customer-kpi-settings/customer-kpi-settings.component').then(m => m.CustomerKpiSettingsComponent),\n    canActivate: [kpisListGuard],\n    children: [\n      {\n        path: ':kpiKey',\n        loadComponent: () => import('./components/customer-kpi-list/customer-kpi-list.component').then(m => m.CustomerKpiListComponent),\n      },\n    ],\n  },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/duplicate-business-case/duplicate-business-case.routing.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}, {"type": "named", "moduleSpecifier": "./guards/duplicate-business-case.guard", "defaultImport": null, "namedImports": [{"name": "duplicateBusinessCaseGuard", "alias": null}], "namespaceImport": null, "fullText": "import { duplicateBusinessCaseGuard } from './guards/duplicate-business-case.guard';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: '',\n    loadComponent: () =>\n      import(\n        './components/duplicate-business-case/duplicate-business-case.component'\n      ).then((m) => m.DuplicateBusinessCaseComponent),\n    canActivate: [duplicateBusinessCaseGuard],\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () =>\n      import(\n        './components/duplicate-business-case/duplicate-business-case.component'\n      ).then((m) => m.DuplicateBusinessCaseComponent),\n    canActivate: [duplicateBusinessCaseGuard],\n  },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/duplicate-business-case/duplicate-business-case.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@ng-bootstrap/ng-bootstrap", "defaultImport": null, "namedImports": [{"name": "NgbAccordionDirective", "alias": null}], "namespaceImport": null, "fullText": "import { NgbAccordionDirective } from '@ng-bootstrap/ng-bootstrap';"}, {"type": "named", "moduleSpecifier": "@ngrx/effects", "defaultImport": null, "namedImports": [{"name": "provideEffects", "alias": null}], "namespaceImport": null, "fullText": "import { provideEffects } from '@ngrx/effects';"}, {"type": "named", "moduleSpecifier": "@ngrx/store", "defaultImport": null, "namedImports": [{"name": "provideState", "alias": null}], "namespaceImport": null, "fullText": "import { provideState } from '@ngrx/store';"}, {"type": "named", "moduleSpecifier": "./+state/effects/duplicate-business-case.effects", "defaultImport": null, "namedImports": [{"name": "DuplicateBusinessCaseEffects", "alias": null}], "namespaceImport": null, "fullText": "import { DuplicateBusinessCaseEffects } from './+state/effects/duplicate-business-case.effects';"}, {"type": "named", "moduleSpecifier": "./+state/reducers/duplicate-business-case.reducer", "defaultImport": null, "namedImports": [{"name": "duplicateBusinessCaseFeature", "alias": null}], "namespaceImport": null, "fullText": "import { duplicateBusinessCaseFeature } from './+state/reducers/duplicate-business-case.reducer';"}, {"type": "named", "moduleSpecifier": "./duplicate-business-case.routing.module", "defaultImport": null, "namedImports": [{"name": "DuplicateBusinessCaseRoutingModule", "alias": null}], "namespaceImport": null, "fullText": "import { DuplicateBusinessCaseRoutingModule } from './duplicate-business-case.routing.module';"}], "hasRoutesConstant": false, "routesConstant": null, "error": null}, {"filePath": "apps/fincloud/src/app/digital-signature/digital-signature.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "./digital-signature-routing.module", "defaultImport": null, "namedImports": [{"name": "DigitalSignatureRoutingModule", "alias": null}], "namespaceImport": null, "fullText": "import { DigitalSignatureRoutingModule } from './digital-signature-routing.module';"}], "hasRoutesConstant": false, "routesConstant": null, "error": null}, {"filePath": "apps/fincloud/src/app/digital-signature/digital-signature-routing.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}, {"type": "named", "moduleSpecifier": "./guards/redirect.guard", "defaultImport": null, "namedImports": [{"name": "redirectGuard", "alias": null}], "namespaceImport": null, "fullText": "import { redirectGuard } from './guards/redirect.guard';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: '',\n    loadComponent: () => import('./components/digital-signature-dashboard/digital-signature-dashboard.component').then(m => m.DigitalSignatureDashboardComponent),\n  },\n  {\n    path: 'redirect/:signingProcessId',\n    loadComponent: () => import('./components/digital-signature-dashboard/digital-signature-dashboard.component').then(m => m.DigitalSignatureDashboardComponent),\n    canActivate: [redirectGuard],\n  },\n  {\n    path: 'success/:signingProcessId',\n    loadComponent: () => import('./components/signature-session-success/signature-session-success.component').then(m => m.SignatureSessionSuccessComponent),\n  },\n  {\n    path: 'access-code-failed/:signingProcessId',\n    loadComponent: () => import('./components/signature-session-auth-code-failed/signature-session-auth-code-failed.component').then(m => m.SignatureSessionAuthCodeFailedComponent),\n  },\n  {\n    path: 'error/:signingProcessId',\n    loadComponent: () => import('./components/signature-session-error/signature-session-error.component').then(m => m.SignatureSessionErrorComponent),\n  },\n  {\n    path: 'session-expired/:signingProcessId',\n    loadComponent: () => import('./components/signature-session-expired/signature-session-expired.component').then(m => m.SignatureSessionExpiredComponent),\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./components/digital-signature-dashboard/digital-signature-dashboard.component').then(m => m.DigitalSignatureDashboardComponent),\n  },\n  {\n    path: 'redirect/:signingProcessId',\n    loadComponent: () => import('./components/digital-signature-dashboard/digital-signature-dashboard.component').then(m => m.DigitalSignatureDashboardComponent),\n    canActivate: [redirectGuard],\n  },\n  {\n    path: 'success/:signingProcessId',\n    loadComponent: () => import('./components/signature-session-success/signature-session-success.component').then(m => m.SignatureSessionSuccessComponent),\n  },\n  {\n    path: 'access-code-failed/:signingProcessId',\n    loadComponent: () => import('./components/signature-session-auth-code-failed/signature-session-auth-code-failed.component').then(m => m.SignatureSessionAuthCodeFailedComponent),\n  },\n  {\n    path: 'error/:signingProcessId',\n    loadComponent: () => import('./components/signature-session-error/signature-session-error.component').then(m => m.SignatureSessionErrorComponent),\n  },\n  {\n    path: 'session-expired/:signingProcessId',\n    loadComponent: () => import('./components/signature-session-expired/signature-session-expired.component').then(m => m.SignatureSessionExpiredComponent),\n  },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/dashboard/dashboard.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/common", "defaultImport": null, "namedImports": [{"name": "C<PERSON><PERSON>cyPipe", "alias": null}, {"name": "DecimalPipe", "alias": null}, {"name": "Percent<PERSON><PERSON>e", "alias": null}], "namespaceImport": null, "fullText": "import { C<PERSON>rencyPipe, DecimalPipe, PercentPipe } from '@angular/common';"}, {"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@fincloud/core/pipes", "defaultImport": null, "namedImports": [{"name": "RemoveTrailingZerosPipe", "alias": null}], "namespaceImport": null, "fullText": "import { RemoveTrailingZerosPipe } from '@fincloud/core/pipes';"}, {"type": "named", "moduleSpecifier": "@fincloud/state/utils", "defaultImport": null, "namedImports": [{"name": "DASHBOARD_FEATURE_KEY", "alias": null}], "namespaceImport": null, "fullText": "import { DASHBOARD_FEATURE_KEY } from '@fincloud/state/utils';"}, {"type": "named", "moduleSpecifier": "@ngrx/effects", "defaultImport": null, "namedImports": [{"name": "provideEffects", "alias": null}], "namespaceImport": null, "fullText": "import { provideEffects } from '@ngrx/effects';"}, {"type": "named", "moduleSpecifier": "@ngrx/store", "defaultImport": null, "namedImports": [{"name": "provideState", "alias": null}], "namespaceImport": null, "fullText": "import { provideState } from '@ngrx/store';"}, {"type": "namespace", "moduleSpecifier": "./+state/effects", "defaultImport": null, "namedImports": [], "namespaceImport": "effects", "fullText": "import * as effects from './+state/effects';"}, {"type": "named", "moduleSpecifier": "./+state/reducers/dashboard.reducer", "defaultImport": null, "namedImports": [{"name": "dashboardReducer", "alias": null}], "namespaceImport": null, "fullText": "import { dashboardReducer } from './+state/reducers/dashboard.reducer';"}, {"type": "named", "moduleSpecifier": "./dashboard-routing.module", "defaultImport": null, "namedImports": [{"name": "DashboardRoutingModule", "alias": null}], "namespaceImport": null, "fullText": "import { DashboardRoutingModule } from './dashboard-routing.module';"}], "hasRoutesConstant": false, "routesConstant": null, "error": null}, {"filePath": "apps/fincloud/src/app/dashboard/dashboard-routing.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}, {"type": "named", "moduleSpecifier": "./guards/dashboard.guard", "defaultImport": null, "namedImports": [{"name": "dashboardGuard", "alias": null}], "namespaceImport": null, "fullText": "import { dashboardGuard } from './guards/dashboard.guard';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: '',\n    canActivate: [dashboardGuard],\n    loadComponent: () => import('./components/dashboard/dashboard.component').then(m => m.DashboardComponent),\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: '',\n    canActivate: [dashboardGuard],\n    loadComponent: () => import('./components/dashboard/dashboard.component').then(m => m.DashboardComponent),\n  },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/customer-master-data/customer-master-data.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "./customer-master-data-routing.module", "defaultImport": null, "namedImports": [{"name": "CustomerMasterDataRoutingModule", "alias": null}], "namespaceImport": null, "fullText": "import { CustomerMasterDataRoutingModule } from './customer-master-data-routing.module';"}], "hasRoutesConstant": false, "routesConstant": null, "error": null}, {"filePath": "apps/fincloud/src/app/customer-master-data/customer-master-data-routing.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: '',\n    loadComponent: () => import('./components/customer-master-data/customer-master-data.component').then(m => m.CustomerMasterDataComponent),\n    children: [\n      {\n        path: 'details',\n        loadComponent: () => import('./components/customer-details/customer-details.component').then(m => m.CustomerDetailsComponent),\n      },\n      {\n        path: 'security',\n        loadComponent: () => import('./components/customer-security/customer-security.component').then(m => m.CustomerSecurityComponent),\n      },\n      {\n        path: 'kpi-settings',\n        loadChildren: () =>\n          import('../kpis/kpis.module').then((m) => m.KpisModule),\n      },\n      {\n        path: 'collaboration',\n        loadComponent: () => import('./components/customer-collaboration/customer-collaboration.component').then(m => m.CustomerCollaborationComponent),\n      },\n    ],\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./components/customer-master-data/customer-master-data.component').then(m => m.CustomerMasterDataComponent),\n    children: [\n      {\n        path: 'details',\n        loadComponent: () => import('./components/customer-details/customer-details.component').then(m => m.CustomerDetailsComponent),\n      },\n      {\n        path: 'security',\n        loadComponent: () => import('./components/customer-security/customer-security.component').then(m => m.CustomerSecurityComponent),\n      },\n      {\n        path: 'kpi-settings',\n        loadChildren: () =>\n          import('../kpis/kpis.module').then((m) => m.KpisModule),\n      },\n      {\n        path: 'collaboration',\n        loadComponent: () => import('./components/customer-collaboration/customer-collaboration.component').then(m => m.CustomerCollaborationComponent),\n      },\n    ],\n  },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/create-business-case/create-business-case.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: '',\n    loadComponent: () => import('./components/create-business-case/create-business-case.component').then(m => m.CreateBusinessCaseComponent),\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./components/create-business-case/create-business-case.component').then(m => m.CreateBusinessCaseComponent),\n  },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/contract-management/contract-management.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/common", "defaultImport": null, "namedImports": [{"name": "C<PERSON><PERSON>cyPipe", "alias": null}], "namespaceImport": null, "fullText": "import { C<PERSON>rencyPipe } from '@angular/common';"}, {"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@fincloud/core/pipes", "defaultImport": null, "namedImports": [{"name": "InitialsPipe", "alias": null}], "namespaceImport": null, "fullText": "import { InitialsPipe } from '@fincloud/core/pipes';"}, {"type": "named", "moduleSpecifier": "./contract-management-routing.module", "defaultImport": null, "namedImports": [{"name": "ContractManagementRoutingModule", "alias": null}], "namespaceImport": null, "fullText": "import { ContractManagementRoutingModule } from './contract-management-routing.module';"}], "hasRoutesConstant": false, "routesConstant": null, "error": null}, {"filePath": "apps/fincloud/src/app/contract-management/contract-management-routing.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: '',\n    loadComponent: () => import('./components/contract-management-dashboard/contract-management-dashboard.component').then(m => m.ContractManagementDashboardComponent),\n    children: [\n      {\n        path: '',\n        pathMatch: 'full',\n        redirectTo: 'contracts',\n      },\n      {\n        path: 'contracts',\n        loadComponent: () => import('./components/contract-list/contract-list.component').then(m => m.ContractListComponent),\n      },\n      // {\n      //   path: 'templates',\n      //   component: TemplateListComponent,\n      // },\n      // {\n      //   path: 'templates/:id/edit',\n      //   component: TextEditorComponent\n      // }\n    ],\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./components/contract-management-dashboard/contract-management-dashboard.component').then(m => m.ContractManagementDashboardComponent),\n    children: [\n      {\n        path: '',\n        pathMatch: 'full',\n        redirectTo: 'contracts',\n      },\n      {\n        path: 'contracts',\n        loadComponent: () => import('./components/contract-list/contract-list.component').then(m => m.ContractListComponent),\n      },\n      // {\n      //   path: 'templates',\n      //   component: TemplateListComponent,\n      // },\n      // {\n      //   path: 'templates/:id/edit',\n      //   component: TextEditorComponent\n      // }\n    ],\n  },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/consent/consent.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "./consent-routing.module", "defaultImport": null, "namedImports": [{"name": "ConsentRoutingModule", "alias": null}], "namespaceImport": null, "fullText": "import { ConsentRoutingModule } from './consent-routing.module';"}], "hasRoutesConstant": false, "routesConstant": null, "error": null}, {"filePath": "apps/fincloud/src/app/consent/consent-routing.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: '',\n    loadComponent: () => import('@fincloud/components/navigation').then(m => m.BaseLayoutComponent),\n    children: [\n      {\n        path: '',\n        loadComponent: () => import('./components/terms-of-use/terms-of-use.component').then(m => m.TermsOfUseComponent),\n      },\n    ],\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('@fincloud/components/navigation').then(m => m.BaseLayoutComponent),\n    children: [\n      {\n        path: '',\n        loadComponent: () => import('./components/terms-of-use/terms-of-use.component').then(m => m.TermsOfUseComponent),\n      },\n    ],\n  },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/company-management/company-management.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: '',\n    loadComponent: () => import('./components/company/company.component').then(m => m.CompanyComponent),\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./components/company/company.component').then(m => m.CompanyComponent),\n  },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/company-graph/company-graph.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/common", "defaultImport": null, "namedImports": [{"name": "registerLocaleData", "alias": null}], "namespaceImport": null, "fullText": "import { registerLocaleData } from '@angular/common';"}, {"type": "default", "moduleSpecifier": "@angular/common/locales/de", "defaultImport": "localeDe", "namedImports": [], "namespaceImport": null, "fullText": "import localeDe from '@angular/common/locales/de';"}, {"type": "named", "moduleSpecifier": "@ngrx/effects", "defaultImport": null, "namedImports": [{"name": "provideEffects", "alias": null}], "namespaceImport": null, "fullText": "import { provideEffects } from '@ngrx/effects';"}, {"type": "named", "moduleSpecifier": "@ngrx/store", "defaultImport": null, "namedImports": [{"name": "provideState", "alias": null}], "namespaceImport": null, "fullText": "import { provideState } from '@ngrx/store';"}, {"type": "named", "moduleSpecifier": "./+state/effects/company-graph-documents.effects", "defaultImport": null, "namedImports": [{"name": "CompanyGraphDocumentsEffects", "alias": null}], "namespaceImport": null, "fullText": "import { CompanyGraphDocumentsEffects } from './+state/effects/company-graph-documents.effects';"}, {"type": "named", "moduleSpecifier": "./+state/effects/company-graph-modify-node.effects", "defaultImport": null, "namedImports": [{"name": "CompanyGraphModifyNodeEffects", "alias": null}], "namespaceImport": null, "fullText": "import { CompanyGraphModifyNodeEffects } from './+state/effects/company-graph-modify-node.effects';"}, {"type": "named", "moduleSpecifier": "./+state/effects/company-graph-network.effects", "defaultImport": null, "namedImports": [{"name": "CompanyGraphNetworkEffects", "alias": null}], "namespaceImport": null, "fullText": "import { CompanyGraphNetworkEffects } from './+state/effects/company-graph-network.effects';"}, {"type": "named", "moduleSpecifier": "./+state/effects/company-graph-revisions.effects", "defaultImport": null, "namedImports": [{"name": "CompanyGraphRevisionsEffects", "alias": null}], "namespaceImport": null, "fullText": "import { CompanyGraphRevisionsEffects } from './+state/effects/company-graph-revisions.effects';"}, {"type": "named", "moduleSpecifier": "./+state/effects/company-graph-settings.effects", "defaultImport": null, "namedImports": [{"name": "CompanyGraphSettingsEffects", "alias": null}], "namespaceImport": null, "fullText": "import { CompanyGraphSettingsEffects } from './+state/effects/company-graph-settings.effects';"}, {"type": "named", "moduleSpecifier": "./+state/effects/company-graph-update-changes.effects", "defaultImport": null, "namedImports": [{"name": "CompanyGraphUpdateChangesEffects", "alias": null}], "namespaceImport": null, "fullText": "import { CompanyGraphUpdateChangesEffects } from './+state/effects/company-graph-update-changes.effects';"}, {"type": "named", "moduleSpecifier": "./+state/effects/company-graph-visualization.effects", "defaultImport": null, "namedImports": [{"name": "CompanyGraphVisualizationEffects", "alias": null}], "namespaceImport": null, "fullText": "import { CompanyGraphVisualizationEffects } from './+state/effects/company-graph-visualization.effects';"}, {"type": "named", "moduleSpecifier": "./+state/reducers/company-graph.reducer", "defaultImport": null, "namedImports": [{"name": "companyGraphFeature", "alias": null}], "namespaceImport": null, "fullText": "import { companyGraphFeature } from './+state/reducers/company-graph.reducer';"}, {"type": "named", "moduleSpecifier": "./company-graph-routing.module", "defaultImport": null, "namedImports": [{"name": "CompanyGraphRoutingModule", "alias": null}], "namespaceImport": null, "fullText": "import { CompanyGraphRoutingModule } from './company-graph-routing.module';"}, {"type": "named", "moduleSpecifier": "./jointjs/services/graph-gallery.service", "defaultImport": null, "namedImports": [{"name": "GraphGalleryService", "alias": null}], "namespaceImport": null, "fullText": "import { GraphGalleryService } from './jointjs/services/graph-gallery.service';"}, {"type": "named", "moduleSpecifier": "./jointjs/services/graph.service", "defaultImport": null, "namedImports": [{"name": "GraphService", "alias": null}], "namespaceImport": null, "fullText": "import { GraphService } from './jointjs/services/graph.service';"}, {"type": "named", "moduleSpecifier": "./jointjs/services/radial-graph-layout.service", "defaultImport": null, "namedImports": [{"name": "RadialGraphLayoutService", "alias": null}], "namespaceImport": null, "fullText": "import { RadialGraphLayoutService } from './jointjs/services/radial-graph-layout.service';"}], "hasRoutesConstant": false, "routesConstant": null, "error": null}, {"filePath": "apps/fincloud/src/app/company-graph/company-graph-routing.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}, {"type": "named", "moduleSpecifier": "./guards/company-graph.guard", "defaultImport": null, "namedImports": [{"name": "companyGraphGuard", "alias": null}], "namespaceImport": null, "fullText": "import { companyGraphGuard } from './guards/company-graph.guard';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: '',\n    loadComponent: () => import('./components/company-graph-container/company-graph-container.component').then(m => m.CompanyGraphContainerComponent),\n    canActivate: [companyGraphGuard],\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./components/company-graph-container/company-graph-container.component').then(m => m.CompanyGraphContainerComponent),\n    canActivate: [companyGraphGuard],\n  },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/company-analysis/company-analysis.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/common", "defaultImport": null, "namedImports": [{"name": "DatePipe", "alias": null}, {"name": "Percent<PERSON><PERSON>e", "alias": null}], "namespaceImport": null, "fullText": "import { DatePipe, PercentPipe } from '@angular/common';"}, {"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@fincloud/core/pipes", "defaultImport": null, "namedImports": [{"name": "RemoveTrailingZerosPipe", "alias": null}], "namespaceImport": null, "fullText": "import { RemoveTrailingZerosPipe } from '@fincloud/core/pipes';"}, {"type": "named", "moduleSpecifier": "@fincloud/state/document", "defaultImport": null, "namedImports": [{"name": "StateLibDocumentEffects", "alias": null}], "namespaceImport": null, "fullText": "import { StateLibDocumentEffects } from '@fincloud/state/document';"}, {"type": "named", "moduleSpecifier": "@ngrx/effects", "defaultImport": null, "namedImports": [{"name": "provideEffects", "alias": null}], "namespaceImport": null, "fullText": "import { provideEffects } from '@ngrx/effects';"}, {"type": "named", "moduleSpecifier": "@ngrx/store", "defaultImport": null, "namedImports": [{"name": "provideState", "alias": null}], "namespaceImport": null, "fullText": "import { provideState } from '@ngrx/store';"}, {"type": "named", "moduleSpecifier": "@fincloud/ui/modal", "defaultImport": null, "namedImports": [{"name": "FIN_MODAL_DEFAULT_OPTIONS", "alias": null}, {"name": "FIN_MODAL_REF_PROVIDER", "alias": null}, {"name": "FinModalService", "alias": null}], "namespaceImport": null, "fullText": "import {\n  FIN_MODAL_DEFAULT_OPTIONS,\n  FIN_MODAL_REF_PROVIDER,\n  FinModalService,\n} from '@fincloud/ui/modal';"}, {"type": "named", "moduleSpecifier": "./+state", "defaultImport": null, "namedImports": [{"name": "companyAnalysisFeature", "alias": null}], "namespaceImport": null, "fullText": "import { companyAnalysisFeature } from './+state';"}, {"type": "namespace", "moduleSpecifier": "./+state/effects", "defaultImport": null, "namedImports": [], "namespaceImport": "effects", "fullText": "import * as effects from './+state/effects';"}, {"type": "named", "moduleSpecifier": "./company-analysis-routing.module", "defaultImport": null, "namedImports": [{"name": "CompanyAnalysisRoutingModule", "alias": null}], "namespaceImport": null, "fullText": "import { CompanyAnalysisRoutingModule } from './company-analysis-routing.module';"}, {"type": "named", "moduleSpecifier": "./services/company-modal.service", "defaultImport": null, "namedImports": [{"name": "CompanyModalService", "alias": null}], "namespaceImport": null, "fullText": "import { CompanyModalService } from './services/company-modal.service';"}], "hasRoutesConstant": false, "routesConstant": null, "error": null}, {"filePath": "apps/fincloud/src/app/company-analysis/company-analysis-routing.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}, {"type": "named", "moduleSpecifier": "@fincloud/neoshare/business-case", "defaultImport": null, "namedImports": [{"name": "canAccessCompanyAnalysisDataRoomGuard", "alias": null}, {"name": "privateCompanyAnalysisGuard", "alias": null}], "namespaceImport": null, "fullText": "import {\n  canAccessCompanyAnalysisDataRoomGuard,\n  privateCompanyAnalysisGuard,\n} from '@fincloud/neoshare/business-case';"}, {"type": "named", "moduleSpecifier": "@fincloud/neoshare/guards", "defaultImport": null, "namedImports": [{"name": "companyGuard", "alias": null}], "namespaceImport": null, "fullText": "import { companyGuard } from '@fincloud/neoshare/guards';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: ':id',\n    redirectTo: ':id/data-room',\n  },\n  {\n    path: ':id',\n    canActivate: [companyGuard],\n    loadComponent: () => import('./components/company-information/company-information.component').then(m => m.CompanyInformationComponent),\n    children: [\n      {\n        path: 'data-room',\n        canActivate: [\n          privateCompanyAnalysisGuard,\n          canAccessCompanyAnalysisDataRoomGuard,\n        ],\n        loadComponent: () => import('./components/company-information/company-information.component').then(m => m.CompanyInformationComponent),\n        children: [\n          {\n            path: 'own',\n            loadComponent: () => import('./components/company-information/company-information.component').then(m => m.CompanyInformationComponent),\n          },\n          {\n            path: 'shared',\n            loadComponent: () => import('./components/share-cadr-modal/share-cadr-modal.component').then(m => m.ShareCadrModalComponent),\n          },\n        ],\n      },\n      {\n        path: 'network',\n        loadChildren: () =>\n          import('../company-graph/company-graph.module').then(\n            (m) => m.CompanyGraphModule,\n          ),\n      },\n      {\n        path: 'documents',\n        loadComponent: () => import('./components/company-information/company-information.component').then(m => m.CompanyInformationComponent),\n      },\n      {\n        path: 'branches',\n        canActivate: [privateCompanyAnalysisGuard],\n        loadComponent: () => import('./components/company-branches/company-branches.component').then(m => m.CompanyBranchesComponent),\n      },\n      {\n        path: 'business-cases',\n        loadComponent: () => import('./components/other-business-cases/other-business-cases.component').then(m => m.OtherBusinessCasesComponent),\n      },\n      {\n        path: 'additional-information',\n        loadComponent: () => import('./components/company-information-sections/company-information-sections.component').then(m => m.CompanyInformationSectionsComponent),\n      },\n    ],\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: ':id',\n    redirectTo: ':id/data-room',\n  },\n  {\n    path: ':id',\n    canActivate: [companyGuard],\n    loadComponent: () => import('./components/company-information/company-information.component').then(m => m.CompanyInformationComponent),\n    children: [\n      {\n        path: 'data-room',\n        canActivate: [\n          privateCompanyAnalysisGuard,\n          canAccessCompanyAnalysisDataRoomGuard,\n        ],\n        loadComponent: () => import('./components/company-information/company-information.component').then(m => m.CompanyInformationComponent),\n        children: [\n          {\n            path: 'own',\n            loadComponent: () => import('./components/company-information/company-information.component').then(m => m.CompanyInformationComponent),\n          },\n          {\n            path: 'shared',\n            loadComponent: () => import('./components/share-cadr-modal/share-cadr-modal.component').then(m => m.ShareCadrModalComponent),\n          },\n        ],\n      },\n      {\n        path: 'network',\n        loadChildren: () =>\n          import('../company-graph/company-graph.module').then(\n            (m) => m.CompanyGraphModule,\n          ),\n      },\n      {\n        path: 'documents',\n        loadComponent: () => import('./components/company-information/company-information.component').then(m => m.CompanyInformationComponent),\n      },\n      {\n        path: 'branches',\n        canActivate: [privateCompanyAnalysisGuard],\n        loadComponent: () => import('./components/company-branches/company-branches.component').then(m => m.CompanyBranchesComponent),\n      },\n      {\n        path: 'business-cases',\n        loadComponent: () => import('./components/other-business-cases/other-business-cases.component').then(m => m.OtherBusinessCasesComponent),\n      },\n      {\n        path: 'additional-information',\n        loadComponent: () => import('./components/company-information-sections/company-information-sections.component').then(m => m.CompanyInformationSectionsComponent),\n      },\n    ],\n  },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/cluster-demo/cluster-demo.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@ngrx/effects", "defaultImport": null, "namedImports": [{"name": "provideEffects", "alias": null}], "namespaceImport": null, "fullText": "import { provideEffects } from '@ngrx/effects';"}, {"type": "named", "moduleSpecifier": "@ngrx/store", "defaultImport": null, "namedImports": [{"name": "provideState", "alias": null}], "namespaceImport": null, "fullText": "import { provideState } from '@ngrx/store';"}, {"type": "named", "moduleSpecifier": "./+state/effects/demo-snapshot-common.effects", "defaultImport": null, "namedImports": [{"name": "DemoSnapshotCommonEffects", "alias": null}], "namespaceImport": null, "fullText": "import { DemoSnapshotCommonEffects } from './+state/effects/demo-snapshot-common.effects';"}, {"type": "named", "moduleSpecifier": "./+state/effects/demo-snapshot-copy.effects", "defaultImport": null, "namedImports": [{"name": "DemoSnapshotCopyEffects", "alias": null}], "namespaceImport": null, "fullText": "import { DemoSnapshotCopyEffects } from './+state/effects/demo-snapshot-copy.effects';"}, {"type": "named", "moduleSpecifier": "./+state/effects/demo-snapshot-create.effects", "defaultImport": null, "namedImports": [{"name": "DemoSnapshotCreateEffects", "alias": null}], "namespaceImport": null, "fullText": "import { DemoSnapshotCreateEffects } from './+state/effects/demo-snapshot-create.effects';"}, {"type": "named", "moduleSpecifier": "./+state/effects/demo-snapshot-customers.effects", "defaultImport": null, "namedImports": [{"name": "DemoSnapshotCustomersEffects", "alias": null}], "namespaceImport": null, "fullText": "import { DemoSnapshotCustomersEffects } from './+state/effects/demo-snapshot-customers.effects';"}, {"type": "named", "moduleSpecifier": "./+state/effects/demo-snapshot-delete.effects", "defaultImport": null, "namedImports": [{"name": "DemoSnapshotDeleteEffects", "alias": null}], "namespaceImport": null, "fullText": "import { DemoSnapshotDeleteEffects } from './+state/effects/demo-snapshot-delete.effects';"}, {"type": "named", "moduleSpecifier": "./+state/effects/demo-snapshot-deploy.effects", "defaultImport": null, "namedImports": [{"name": "DemoSnapshotDeployEffects", "alias": null}], "namespaceImport": null, "fullText": "import { DemoSnapshotDeployEffects } from './+state/effects/demo-snapshot-deploy.effects';"}, {"type": "named", "moduleSpecifier": "./+state/effects/demo-snapshot-details-common.effects", "defaultImport": null, "namedImports": [{"name": "DemoSnapshotDetailsCommonEffects", "alias": null}], "namespaceImport": null, "fullText": "import { DemoSnapshotDetailsCommonEffects } from './+state/effects/demo-snapshot-details-common.effects';"}, {"type": "named", "moduleSpecifier": "./+state/effects/demo-snapshot-details-latest-state.effects", "defaultImport": null, "namedImports": [{"name": "DemoSnapshotDetailsLatestStateEffects", "alias": null}], "namespaceImport": null, "fullText": "import { DemoSnapshotDetailsLatestStateEffects } from './+state/effects/demo-snapshot-details-latest-state.effects';"}, {"type": "named", "moduleSpecifier": "./+state/effects/demo-snapshot-edit-general-information.effects", "defaultImport": null, "namedImports": [{"name": "DemoSnapshotEditGeneralInformationEffects", "alias": null}], "namespaceImport": null, "fullText": "import { DemoSnapshotEditGeneralInformationEffects } from './+state/effects/demo-snapshot-edit-general-information.effects';"}, {"type": "named", "moduleSpecifier": "./+state/effects/demo-snapshot-edit-user.effects", "defaultImport": null, "namedImports": [{"name": "DemoSnapshotEditUserEffects", "alias": null}], "namespaceImport": null, "fullText": "import { DemoSnapshotEditUserEffects } from './+state/effects/demo-snapshot-edit-user.effects';"}, {"type": "named", "moduleSpecifier": "./+state/effects/demo-snapshot-list.effects", "defaultImport": null, "namedImports": [{"name": "DemoSnapshotListEffects", "alias": null}], "namespaceImport": null, "fullText": "import { DemoSnapshotListEffects } from './+state/effects/demo-snapshot-list.effects';"}, {"type": "named", "moduleSpecifier": "./+state/effects/demo-snapshot-recall.effects", "defaultImport": null, "namedImports": [{"name": "DemoSnapshotRecallEffects", "alias": null}], "namespaceImport": null, "fullText": "import { DemoSnapshotRecallEffects } from './+state/effects/demo-snapshot-recall.effects';"}, {"type": "named", "moduleSpecifier": "./+state/effects/demo-snapshot-reset.effects", "defaultImport": null, "namedImports": [{"name": "DemoSnapshotResetEffects", "alias": null}], "namespaceImport": null, "fullText": "import { DemoSnapshotResetEffects } from './+state/effects/demo-snapshot-reset.effects';"}, {"type": "named", "moduleSpecifier": "./+state/reducers/demo-snapshot-create.reducer", "defaultImport": null, "namedImports": [{"name": "snapshotCreateFeature", "alias": null}], "namespaceImport": null, "fullText": "import { snapshotCreateFeature } from './+state/reducers/demo-snapshot-create.reducer';"}, {"type": "named", "moduleSpecifier": "./+state/reducers/demo-snapshot-details.reducer", "defaultImport": null, "namedImports": [{"name": "snapshotDetailsFeature", "alias": null}], "namespaceImport": null, "fullText": "import { snapshotDetailsFeature } from './+state/reducers/demo-snapshot-details.reducer';"}, {"type": "named", "moduleSpecifier": "./+state/reducers/demo-snapshot.reducer", "defaultImport": null, "namedImports": [{"name": "demoSnapshotReducer", "alias": null}, {"name": "demoSnapshotStateSlice", "alias": null}], "namespaceImport": null, "fullText": "import {\n  demoSnapshotReducer,\n  demoSnapshotStateSlice,\n} from './+state/reducers/demo-snapshot.reducer';"}, {"type": "named", "moduleSpecifier": "./cluster-demo-routing.module", "defaultImport": null, "namedImports": [{"name": "ClusterDemoRoutingModule", "alias": null}], "namespaceImport": null, "fullText": "import { ClusterDemoRoutingModule } from './cluster-demo-routing.module';"}, {"type": "named", "moduleSpecifier": "./services/demo-snapshot-helper.service", "defaultImport": null, "namedImports": [{"name": "DemoSnapshotHelperService", "alias": null}], "namespaceImport": null, "fullText": "import { DemoSnapshotHelperService } from './services/demo-snapshot-helper.service';"}], "hasRoutesConstant": false, "routesConstant": null, "error": null}, {"filePath": "apps/fincloud/src/app/cluster-demo/cluster-demo-routing.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}, {"type": "named", "moduleSpecifier": "./guards/snapshot-copy.guard", "defaultImport": null, "namedImports": [{"name": "snapshotCopyGuard", "alias": null}], "namespaceImport": null, "fullText": "import { snapshotCopyGuard } from './guards/snapshot-copy.guard';"}, {"type": "named", "moduleSpecifier": "./guards/snapshot-create.guard", "defaultImport": null, "namedImports": [{"name": "snapshotCreateGuard", "alias": null}], "namespaceImport": null, "fullText": "import { snapshotCreateGuard } from './guards/snapshot-create.guard';"}, {"type": "named", "moduleSpecifier": "./guards/snapshot-details.guard", "defaultImport": null, "namedImports": [{"name": "snapshotDetailsGuard", "alias": null}], "namespaceImport": null, "fullText": "import { snapshotDetailsGuard } from './guards/snapshot-details.guard';"}, {"type": "named", "moduleSpecifier": "./guards/snapshot-edit.guard", "defaultImport": null, "namedImports": [{"name": "SnapshotEditGuard", "alias": null}], "namespaceImport": null, "fullText": "import { SnapshotEditGuard } from './guards/snapshot-edit.guard';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: '',\n    loadComponent: () => import('./components/demo-snapshot/demo-snapshot.component').then(m => m.DemoSnapshotComponent),\n  },\n  {\n    path: 'create',\n    loadComponent: () => import('./components/create-demo-snapshot-stepper/create-demo-snapshot-stepper.component').then(m => m.CreateDemoSnapshotStepperComponent),\n    canActivate: [snapshotCreateGuard],\n  },\n  {\n    path: ':uniqueIdentifier',\n    loadComponent: () => import('./components/snapshot-details-page/snapshot-details-page.component').then(m => m.SnapshotDetailsPageComponent),\n    canActivate: [snapshotDetailsGuard],\n  },\n  {\n    path: 'copy/:uniqueIdentifier',\n    loadComponent: () => import('./components/create-demo-snapshot-stepper/create-demo-snapshot-stepper.component').then(m => m.CreateDemoSnapshotStepperComponent),\n    canActivate: [snapshotCopyGuard],\n  },\n  {\n    path: ':uniqueIdentifier/edit',\n    loadComponent: () => import('./components/edit-snapshot/edit-snapshot.component').then(m => m.EditSnapshotComponent),\n    canActivate: [SnapshotEditGuard],\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./components/demo-snapshot/demo-snapshot.component').then(m => m.DemoSnapshotComponent),\n  },\n  {\n    path: 'create',\n    loadComponent: () => import('./components/create-demo-snapshot-stepper/create-demo-snapshot-stepper.component').then(m => m.CreateDemoSnapshotStepperComponent),\n    canActivate: [snapshotCreateGuard],\n  },\n  {\n    path: ':uniqueIdentifier',\n    loadComponent: () => import('./components/snapshot-details-page/snapshot-details-page.component').then(m => m.SnapshotDetailsPageComponent),\n    canActivate: [snapshotDetailsGuard],\n  },\n  {\n    path: 'copy/:uniqueIdentifier',\n    loadComponent: () => import('./components/create-demo-snapshot-stepper/create-demo-snapshot-stepper.component').then(m => m.CreateDemoSnapshotStepperComponent),\n    canActivate: [snapshotCopyGuard],\n  },\n  {\n    path: ':uniqueIdentifier/edit',\n    loadComponent: () => import('./components/edit-snapshot/edit-snapshot.component').then(m => m.EditSnapshotComponent),\n    canActivate: [SnapshotEditGuard],\n  },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/chat/chat.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@ngrx/effects", "defaultImport": null, "namedImports": [{"name": "provideEffects", "alias": null}], "namespaceImport": null, "fullText": "import { provideEffects } from '@ngrx/effects';"}, {"type": "named", "moduleSpecifier": "./+state/effects/chat-export.effects", "defaultImport": null, "namedImports": [{"name": "ChatExportEffects", "alias": null}], "namespaceImport": null, "fullText": "import { ChatExportEffects } from './+state/effects/chat-export.effects';"}, {"type": "named", "moduleSpecifier": "./+state/effects/chat.effects", "defaultImport": null, "namedImports": [{"name": "ChatEffects", "alias": null}], "namespaceImport": null, "fullText": "import { ChatEffects } from './+state/effects/chat.effects';"}, {"type": "named", "moduleSpecifier": "./chat-routing.module", "defaultImport": null, "namedImports": [{"name": "ChatRoutingModule", "alias": null}], "namespaceImport": null, "fullText": "import { ChatRoutingModule } from './chat-routing.module';"}], "hasRoutesConstant": false, "routesConstant": null, "error": null}, {"filePath": "apps/fincloud/src/app/chat/chat-routing.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: '',\n    loadComponent: () => import('./components/chat-dashboard/chat-dashboard.component').then(m => m.ChatDashboardComponent),\n    children: [\n      {\n        path: 'business-case',\n        loadComponent: () => import('./components/chat-dashboard/chat-dashboard.component').then(m => m.ChatDashboardComponent),\n      },\n      {\n        path: 'internal',\n        loadComponent: () => import('./components/chat-dashboard/chat-dashboard.component').then(m => m.ChatDashboardComponent),\n        children: [\n          {\n            path: ':chatId',\n            loadComponent: () => import('./components/chat-dashboard/chat-dashboard.component').then(m => m.ChatDashboardComponent),\n          },\n        ],\n      },\n      {\n        path: 'bilateral',\n        loadComponent: () => import('./components/chat-dashboard/chat-dashboard.component').then(m => m.ChatDashboardComponent),\n        children: [\n          {\n            path: ':chatId',\n            loadComponent: () => import('./components/chat-dashboard/chat-dashboard.component').then(m => m.ChatDashboardComponent),\n          },\n        ],\n      },\n      {\n        path: 'topic',\n        loadComponent: () => import('./components/chat-dashboard/chat-dashboard.component').then(m => m.ChatDashboardComponent),\n        children: [\n          {\n            path: ':chatId',\n            loadComponent: () => import('./components/chat-dashboard/chat-dashboard.component').then(m => m.ChatDashboardComponent),\n          },\n        ],\n      },\n      {\n        path: 'archive',\n        loadComponent: () => import('./components/chat-dashboard/chat-dashboard.component').then(m => m.ChatDashboardComponent),\n      },\n    ],\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./components/chat-dashboard/chat-dashboard.component').then(m => m.ChatDashboardComponent),\n    children: [\n      {\n        path: 'business-case',\n        loadComponent: () => import('./components/chat-dashboard/chat-dashboard.component').then(m => m.ChatDashboardComponent),\n      },\n      {\n        path: 'internal',\n        loadComponent: () => import('./components/chat-dashboard/chat-dashboard.component').then(m => m.ChatDashboardComponent),\n        children: [\n          {\n            path: ':chatId',\n            loadComponent: () => import('./components/chat-dashboard/chat-dashboard.component').then(m => m.ChatDashboardComponent),\n          },\n        ],\n      },\n      {\n        path: 'bilateral',\n        loadComponent: () => import('./components/chat-dashboard/chat-dashboard.component').then(m => m.ChatDashboardComponent),\n        children: [\n          {\n            path: ':chatId',\n            loadComponent: () => import('./components/chat-dashboard/chat-dashboard.component').then(m => m.ChatDashboardComponent),\n          },\n        ],\n      },\n      {\n        path: 'topic',\n        loadComponent: () => import('./components/chat-dashboard/chat-dashboard.component').then(m => m.ChatDashboardComponent),\n        children: [\n          {\n            path: ':chatId',\n            loadComponent: () => import('./components/chat-dashboard/chat-dashboard.component').then(m => m.ChatDashboardComponent),\n          },\n        ],\n      },\n      {\n        path: 'archive',\n        loadComponent: () => import('./components/chat-dashboard/chat-dashboard.component').then(m => m.ChatDashboardComponent),\n      },\n    ],\n  },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/cases/cases.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/common", "defaultImport": null, "namedImports": [{"name": "DecimalPipe", "alias": null}], "namespaceImport": null, "fullText": "import { DecimalPipe } from '@angular/common';"}, {"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@fincloud/state/utils", "defaultImport": null, "namedImports": [{"name": "CASES_FEATURE_KEY", "alias": null}], "namespaceImport": null, "fullText": "import { CASES_FEATURE_KEY } from '@fincloud/state/utils';"}, {"type": "named", "moduleSpecifier": "@ngrx/effects", "defaultImport": null, "namedImports": [{"name": "provideEffects", "alias": null}], "namespaceImport": null, "fullText": "import { provideEffects } from '@ngrx/effects';"}, {"type": "named", "moduleSpecifier": "@ngrx/store", "defaultImport": null, "namedImports": [{"name": "provideState", "alias": null}], "namespaceImport": null, "fullText": "import { provideState } from '@ngrx/store';"}, {"type": "named", "moduleSpecifier": "./+state", "defaultImport": null, "namedImports": [{"name": "casesReducer", "alias": null}], "namespaceImport": null, "fullText": "import { casesReducer } from './+state';"}, {"type": "namespace", "moduleSpecifier": "./+state/effects", "defaultImport": null, "namedImports": [], "namespaceImport": "effects", "fullText": "import * as effects from './+state/effects';"}, {"type": "named", "moduleSpecifier": "./cases-routing.module", "defaultImport": null, "namedImports": [{"name": "CasesRoutingModule", "alias": null}], "namespaceImport": null, "fullText": "import { CasesRoutingModule } from './cases-routing.module';"}], "hasRoutesConstant": false, "routesConstant": null, "error": null}, {"filePath": "apps/fincloud/src/app/cases/cases-routing.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}, {"type": "named", "moduleSpecifier": "./guards/cases.guard", "defaultImport": null, "namedImports": [{"name": "casesGuard", "alias": null}], "namespaceImport": null, "fullText": "import { casesGuard } from './guards/cases.guard';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: '',\n    canActivate: [casesGuard],\n    loadComponent: () => import('./components/case-overview/case-overview.component').then(m => m.CaseOverviewComponent),\n    children: [\n      {\n        path: 'my-cases',\n        canActivate: [casesGuard],\n        loadComponent: () => import('./components/case-overview/case-overview.component').then(m => m.CaseOverviewComponent),\n      },\n      {\n        path: 'customer-cases',\n        loadComponent: () => import('./components/case-overview/case-overview.component').then(m => m.CaseOverviewComponent),\n      },\n      {\n        path: 'applications-invitations',\n        canActivate: [casesGuard],\n        loadComponent: () => import('./components/case-overview/case-overview.component').then(m => m.CaseOverviewComponent),\n      },\n      {\n        path: 'invitations',\n        canActivate: [casesGuard],\n        loadComponent: () => import('./components/case-overview/case-overview.component').then(m => m.CaseOverviewComponent),\n      },\n    ],\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: '',\n    canActivate: [casesGuard],\n    loadComponent: () => import('./components/case-overview/case-overview.component').then(m => m.CaseOverviewComponent),\n    children: [\n      {\n        path: 'my-cases',\n        canActivate: [casesGuard],\n        loadComponent: () => import('./components/case-overview/case-overview.component').then(m => m.CaseOverviewComponent),\n      },\n      {\n        path: 'customer-cases',\n        loadComponent: () => import('./components/case-overview/case-overview.component').then(m => m.CaseOverviewComponent),\n      },\n      {\n        path: 'applications-invitations',\n        canActivate: [casesGuard],\n        loadComponent: () => import('./components/case-overview/case-overview.component').then(m => m.CaseOverviewComponent),\n      },\n      {\n        path: 'invitations',\n        canActivate: [casesGuard],\n        loadComponent: () => import('./components/case-overview/case-overview.component').then(m => m.CaseOverviewComponent),\n      },\n    ],\n  },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/business-case-template-management/business-case-template-management.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@ngrx/effects", "defaultImport": null, "namedImports": [{"name": "provideEffects", "alias": null}], "namespaceImport": null, "fullText": "import { provideEffects } from '@ngrx/effects';"}, {"type": "named", "moduleSpecifier": "@ngrx/store", "defaultImport": null, "namedImports": [{"name": "provideState", "alias": null}], "namespaceImport": null, "fullText": "import { provideState } from '@ngrx/store';"}, {"type": "named", "moduleSpecifier": "./+state/effects/business-case-template-management.effects", "defaultImport": null, "namedImports": [{"name": "BusinessCaseTemplateManagementEffects", "alias": null}], "namespaceImport": null, "fullText": "import { BusinessCaseTemplateManagementEffects } from './+state/effects/business-case-template-management.effects';"}, {"type": "named", "moduleSpecifier": "./+state/reducers/business-case-template-management.reducer", "defaultImport": null, "namedImports": [{"name": "businessCaseTemplateManagementReducer", "alias": null}, {"name": "businessCaseTemplateManagementStateSlice", "alias": null}, {"name": "initialTemplatesState", "alias": null}], "namespaceImport": null, "fullText": "import {\n  businessCaseTemplateManagementReducer,\n  businessCaseTemplateManagementStateSlice,\n  initialTemplatesState,\n} from './+state/reducers/business-case-template-management.reducer';"}, {"type": "named", "moduleSpecifier": "./business-case-template-management-routing.module", "defaultImport": null, "namedImports": [{"name": "BusinessCaseTemplateManagementRoutingModule", "alias": null}], "namespaceImport": null, "fullText": "import { BusinessCaseTemplateManagementRoutingModule } from './business-case-template-management-routing.module';"}], "hasRoutesConstant": false, "routesConstant": null, "error": null}, {"filePath": "apps/fincloud/src/app/business-case-template-management/business-case-template-management-routing.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: '',\n    loadComponent: () => import('./components/business-case-template-management/business-case-template-management.component').then(m => m.BusinessCaseTemplateManagementComponent),\n    pathMatch: 'prefix',\n  },\n  {\n    path: 'new-case-template',\n    loadComponent: () => import('./components/business-case-template-management/business-case-template-management.component').then(m => m.BusinessCaseTemplateManagementComponent),\n    pathMatch: 'prefix',\n    data: { newTemplate: true, template: 'case' },\n  },\n  {\n    path: 'new-cadr-template',\n    loadComponent: () => import('./components/business-case-template-management/business-case-template-management.component').then(m => m.BusinessCaseTemplateManagementComponent),\n    pathMatch: 'prefix',\n    data: { newTemplate: true, template: 'CADR' },\n  },\n  {\n    path: 'cadr/:id',\n    loadComponent: () => import('./components/business-case-template-management/business-case-template-management.component').then(m => m.BusinessCaseTemplateManagementComponent),\n    pathMatch: 'prefix',\n  },\n  {\n    path: ':id',\n    loadComponent: () => import('./components/business-case-template-management/business-case-template-management.component').then(m => m.BusinessCaseTemplateManagementComponent),\n    pathMatch: 'prefix',\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./components/business-case-template-management/business-case-template-management.component').then(m => m.BusinessCaseTemplateManagementComponent),\n    pathMatch: 'prefix',\n  },\n  {\n    path: 'new-case-template',\n    loadComponent: () => import('./components/business-case-template-management/business-case-template-management.component').then(m => m.BusinessCaseTemplateManagementComponent),\n    pathMatch: 'prefix',\n    data: { newTemplate: true, template: 'case' },\n  },\n  {\n    path: 'new-cadr-template',\n    loadComponent: () => import('./components/business-case-template-management/business-case-template-management.component').then(m => m.BusinessCaseTemplateManagementComponent),\n    pathMatch: 'prefix',\n    data: { newTemplate: true, template: 'CADR' },\n  },\n  {\n    path: 'cadr/:id',\n    loadComponent: () => import('./components/business-case-template-management/business-case-template-management.component').then(m => m.BusinessCaseTemplateManagementComponent),\n    pathMatch: 'prefix',\n  },\n  {\n    path: ':id',\n    loadComponent: () => import('./components/business-case-template-management/business-case-template-management.component').then(m => m.BusinessCaseTemplateManagementComponent),\n    pathMatch: 'prefix',\n  },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/business-case-real-estate-fs/business-case-real-estate-financing-structure.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@fincloud/core/pipes", "defaultImport": null, "namedImports": [{"name": "RemoveTrailingZerosPipe", "alias": null}], "namespaceImport": null, "fullText": "import { RemoveTrailingZerosPipe } from '@fincloud/core/pipes';"}, {"type": "named", "moduleSpecifier": "@fincloud/state/utils", "defaultImport": null, "namedImports": [{"name": "FINANCING_DETAILS_FEATURE_KEY", "alias": null}], "namespaceImport": null, "fullText": "import { FINANCING_DETAILS_FEATURE_KEY } from '@fincloud/state/utils';"}, {"type": "named", "moduleSpecifier": "@ng-bootstrap/ng-bootstrap", "defaultImport": null, "namedImports": [{"name": "NgbActiveModal", "alias": null}], "namespaceImport": null, "fullText": "import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';"}, {"type": "named", "moduleSpecifier": "@ngrx/effects", "defaultImport": null, "namedImports": [{"name": "provideEffects", "alias": null}], "namespaceImport": null, "fullText": "import { provideEffects } from '@ngrx/effects';"}, {"type": "named", "moduleSpecifier": "@ngrx/store", "defaultImport": null, "namedImports": [{"name": "provideState", "alias": null}], "namespaceImport": null, "fullText": "import { provideState } from '@ngrx/store';"}, {"type": "named", "moduleSpecifier": "./+state/effects", "defaultImport": null, "namedImports": [{"name": "FinancingDetailsEffects", "alias": null}, {"name": "SearchFinStructureEffects", "alias": null}], "namespaceImport": null, "fullText": "import {\n  FinancingDetailsEffects,\n  SearchFinStructureEffects,\n} from './+state/effects';"}, {"type": "named", "moduleSpecifier": "./+state/effects/teaser-export.effects", "defaultImport": null, "namedImports": [{"name": "TeaserExportEffects", "alias": null}], "namespaceImport": null, "fullText": "import { TeaserExportEffects } from './+state/effects/teaser-export.effects';"}, {"type": "named", "moduleSpecifier": "./+state/reducers", "defaultImport": null, "namedImports": [{"name": "reducers", "alias": null}], "namespaceImport": null, "fullText": "import { reducers } from './+state/reducers';"}, {"type": "named", "moduleSpecifier": "./+state/reducers/teaser-export.reducer", "defaultImport": null, "namedImports": [{"name": "teaserExportFeature", "alias": null}], "namespaceImport": null, "fullText": "import { teaserExportFeature } from './+state/reducers/teaser-export.reducer';"}, {"type": "named", "moduleSpecifier": "./business-case-real-estate-financing-structure-routing.module", "defaultImport": null, "namedImports": [{"name": "BusinessCaseRealEstateFinancingStructureRoutingModule", "alias": null}], "namespaceImport": null, "fullText": "import { BusinessCaseRealEstateFinancingStructureRoutingModule } from './business-case-real-estate-financing-structure-routing.module';"}, {"type": "named", "moduleSpecifier": "./services/refs-content.service", "defaultImport": null, "namedImports": [{"name": "RefsContentService", "alias": null}], "namespaceImport": null, "fullText": "import { RefsContentService } from './services/refs-content.service';"}, {"type": "named", "moduleSpecifier": "./services/refs-financing-partners.service", "defaultImport": null, "namedImports": [{"name": "RefsFinancingPartnersService", "alias": null}], "namespaceImport": null, "fullText": "import { RefsFinancingPartnersService } from './services/refs-financing-partners.service';"}, {"type": "named", "moduleSpecifier": "./services/search-financing.service", "defaultImport": null, "namedImports": [{"name": "SearchFinancingService", "alias": null}], "namespaceImport": null, "fullText": "import { SearchFinancingService } from './services/search-financing.service';"}], "hasRoutesConstant": false, "routesConstant": null, "error": null}, {"filePath": "apps/fincloud/src/app/business-case-real-estate-fs/business-case-real-estate-financing-structure-routing.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}, {"type": "named", "moduleSpecifier": "@fincloud/neoshare/guards", "defaultImport": null, "namedImports": [{"name": "participationRouteGuard", "alias": null}], "namespaceImport": null, "fullText": "import { participationRouteGuard } from '@fincloud/neoshare/guards';"}, {"type": "named", "moduleSpecifier": "@fincloud/neoshare/neogpt-chat", "defaultImport": null, "namedImports": [{"name": "neogptChatActivateGuard", "alias": null}, {"name": "neogptChatDeactivateGuard", "alias": null}], "namespaceImport": null, "fullText": "import {\n  neogptChatActivateGuard,\n  neogptChatDeactivateGuard,\n} from '@fincloud/neoshare/neogpt-chat';"}, {"type": "named", "moduleSpecifier": "@fincloud/types/enums", "defaultImport": null, "namedImports": [{"name": "FinancingDetailsPath", "alias": null}, {"name": "FinancingDetailsSubPage", "alias": null}, {"name": "NeoGptActiveSession", "alias": null}], "namespaceImport": null, "fullText": "import {\n  FinancingDetailsPath,\n  FinancingDetailsSubPage,\n  NeoGptActiveSession,\n} from '@fincloud/types/enums';"}, {"type": "named", "moduleSpecifier": "./guards/financing-details-real-estate-activate.guard", "defaultImport": null, "namedImports": [{"name": "financingDetailsRealEstateActivateGuard", "alias": null}], "namespaceImport": null, "fullText": "import { financingDetailsRealEstateActivateGuard } from './guards/financing-details-real-estate-activate.guard';"}, {"type": "named", "moduleSpecifier": "./guards/financing-structure-deactivate.guard", "defaultImport": null, "namedImports": [{"name": "financingStructureDeactivateGuard", "alias": null}], "namespaceImport": null, "fullText": "import { financingStructureDeactivateGuard } from './guards/financing-structure-deactivate.guard';"}, {"type": "named", "moduleSpecifier": "./guards/financing-structure-resolver.guard", "defaultImport": null, "namedImports": [{"name": "financingStructureResolver", "alias": null}], "namespaceImport": null, "fullText": "import { financingStructureResolver } from './guards/financing-structure-resolver.guard';"}, {"type": "named", "moduleSpecifier": "./guards/initial-shared-groups-resolver", "defaultImport": null, "namedImports": [{"name": "initialSharedGroupsResolver", "alias": null}], "namespaceImport": null, "fullText": "import { initialSharedGroupsResolver } from './guards/initial-shared-groups-resolver';"}, {"type": "named", "moduleSpecifier": "./guards/refs-resolver", "defaultImport": null, "namedImports": [{"name": "refsResolver", "alias": null}], "namespaceImport": null, "fullText": "import { refsResolver } from './guards/refs-resolver';"}, {"type": "named", "moduleSpecifier": "./guards/section-application-real-estate-match.guard", "defaultImport": null, "namedImports": [{"name": "sectionApplicationRealEstateMatchGuard", "alias": null}], "namespaceImport": null, "fullText": "import { sectionApplicationRealEstateMatchGuard } from './guards/section-application-real-estate-match.guard';"}, {"type": "named", "moduleSpecifier": "./guards/section-invitation-real-estate-match.guard", "defaultImport": null, "namedImports": [{"name": "sectionInvitationRealEstateMatchGuard", "alias": null}], "namespaceImport": null, "fullText": "import { sectionInvitationRealEstateMatchGuard } from './guards/section-invitation-real-estate-match.guard';"}, {"type": "named", "moduleSpecifier": "./guards/shared-financing-structure-activate.guard", "defaultImport": null, "namedImports": [{"name": "sharedFinancingStructureActivateGuard", "alias": null}], "namespaceImport": null, "fullText": "import { sharedFinancingStructureActivateGuard } from './guards/shared-financing-structure-activate.guard';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: '',\n    loadComponent: () => import('./components/refs-financing-details/refs-financing-details.component').then(m => m.RefsFinancingDetailsComponent),\n    canActivate: [\n      financingDetailsRealEstateActivateGuard,\n      financingStructureActivateGuard,\n    ],\n    resolve: [refsResolver],\n    children: [\n      {\n        path: FinancingDetailsPath.FINANCING_STRUCTURE,\n        data: {\n          activePath: FinancingDetailsSubPage.FINANCING_STRUCTURE,\n          activeSession: NeoGptActiveSession.FINANCING_DETAILS,\n        },\n        loadComponent: () => import('./components/refs-content/refs-content.component').then(m => m.RefsContentComponent),\n        canActivate: [neogptChatActivateGuard],\n        canDeactivate: [\n          neogptChatDeactivateGuard,\n          financingStructureDeactivateGuard,\n        ],\n        resolve: [financingStructureResolver],\n      },\n      {\n        path: FinancingDetailsPath.SHARED_FINANCING_STRUCTURE,\n        canActivate: [sharedFinancingStructureActivateGuard],\n        canDeactivate: [financingStructureDeactivateGuard],\n        resolve: [initialSharedGroupsResolver],\n        data: {\n          activePath: FinancingDetailsSubPage.SHARED_FINANCING_STRUCTURE,\n        },\n        loadComponent: () => import('./components/refs-shared-financing-structure/refs-shared-financing-structure.component').then(m => m.RefsSharedFinancingStructureComponent),\n      },\n      {\n        path: FinancingDetailsPath.MY_PARTICIPATION,\n        loadComponent: () => import('@fincloud/neoshare/business-case').then(m => m.SectionInvitationComponent),\n        data: { activePath: FinancingDetailsSubPage.MY_PARTICIPATION },\n        canMatch: [sectionInvitationRealEstateMatchGuard],\n      },\n      {\n        path: FinancingDetailsPath.MY_PARTICIPATION,\n        loadComponent: () => import('@fincloud/neoshare/business-case').then(m => m.SectionApplicationComponent),\n        data: { activePath: FinancingDetailsSubPage.MY_PARTICIPATION },\n        canMatch: [sectionApplicationRealEstateMatchGuard],\n      },\n    ],\n  },\n  {\n    path: '**',\n    canActivate: [participationRouteGuard],\n    children: [],\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./components/refs-financing-details/refs-financing-details.component').then(m => m.RefsFinancingDetailsComponent),\n    canActivate: [\n      financingDetailsRealEstateActivateGuard,\n      financingStructureActivateGuard,\n    ],\n    resolve: [refsResolver],\n    children: [\n      {\n        path: FinancingDetailsPath.FINANCING_STRUCTURE,\n        data: {\n          activePath: FinancingDetailsSubPage.FINANCING_STRUCTURE,\n          activeSession: NeoGptActiveSession.FINANCING_DETAILS,\n        },\n        loadComponent: () => import('./components/refs-content/refs-content.component').then(m => m.RefsContentComponent),\n        canActivate: [neogptChatActivateGuard],\n        canDeactivate: [\n          neogptChatDeactivateGuard,\n          financingStructureDeactivateGuard,\n        ],\n        resolve: [financingStructureResolver],\n      },\n      {\n        path: FinancingDetailsPath.SHARED_FINANCING_STRUCTURE,\n        canActivate: [sharedFinancingStructureActivateGuard],\n        canDeactivate: [financingStructureDeactivateGuard],\n        resolve: [initialSharedGroupsResolver],\n        data: {\n          activePath: FinancingDetailsSubPage.SHARED_FINANCING_STRUCTURE,\n        },\n        loadComponent: () => import('./components/refs-shared-financing-structure/refs-shared-financing-structure.component').then(m => m.RefsSharedFinancingStructureComponent),\n      },\n      {\n        path: FinancingDetailsPath.MY_PARTICIPATION,\n        loadComponent: () => import('@fincloud/neoshare/business-case').then(m => m.SectionInvitationComponent),\n        data: { activePath: FinancingDetailsSubPage.MY_PARTICIPATION },\n        canMatch: [sectionInvitationRealEstateMatchGuard],\n      },\n      {\n        path: FinancingDetailsPath.MY_PARTICIPATION,\n        loadComponent: () => import('@fincloud/neoshare/business-case').then(m => m.SectionApplicationComponent),\n        data: { activePath: FinancingDetailsSubPage.MY_PARTICIPATION },\n        canMatch: [sectionApplicationRealEstateMatchGuard],\n      },\n    ],\n  },\n  {\n    path: '**',\n    canActivate: [participationRouteGuard],\n    children: [],\n  },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/business-case-portal/business-case-portal.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "./business-case-portal-routing.module", "defaultImport": null, "namedImports": [{"name": "BusinessCasePortalRoutingModule", "alias": null}], "namespaceImport": null, "fullText": "import { BusinessCasePortalRoutingModule } from './business-case-portal-routing.module';"}], "hasRoutesConstant": false, "routesConstant": null, "error": null}, {"filePath": "apps/fincloud/src/app/business-case-portal/business-case-portal-routing.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[]", "fullText": "const routes: Routes = [];"}, "error": null}, {"filePath": "apps/fincloud/src/app/business-case-miscellaneous-fs/business-case-miscellaneous-financing-structure.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "./business-case-miscellaneous-financing-structure-routing.module", "defaultImport": null, "namedImports": [{"name": "BusinessCaseMiscellaneousFinancingStructureRoutingModule", "alias": null}], "namespaceImport": null, "fullText": "import { BusinessCaseMiscellaneousFinancingStructureRoutingModule } from './business-case-miscellaneous-financing-structure-routing.module';"}], "hasRoutesConstant": false, "routesConstant": null, "error": null}, {"filePath": "apps/fincloud/src/app/business-case-miscellaneous-fs/business-case-miscellaneous-financing-structure-routing.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}, {"type": "named", "moduleSpecifier": "@fincloud/neoshare/guards", "defaultImport": null, "namedImports": [{"name": "participationRouteGuard", "alias": null}], "namespaceImport": null, "fullText": "import { participationRouteGuard } from '@fincloud/neoshare/guards';"}, {"type": "named", "moduleSpecifier": "@fincloud/types/enums", "defaultImport": null, "namedImports": [{"name": "FinancingDetailsPath", "alias": null}], "namespaceImport": null, "fullText": "import { FinancingDetailsPath } from '@fincloud/types/enums';"}, {"type": "named", "moduleSpecifier": "./guards/financing-details-miscellaneous-activate.guard", "defaultImport": null, "namedImports": [{"name": "financingDetailsMiscellaneousActivateGuard", "alias": null}], "namespaceImport": null, "fullText": "import { financingDetailsMiscellaneousActivateGuard } from './guards/financing-details-miscellaneous-activate.guard';"}, {"type": "named", "moduleSpecifier": "./guards/participation-miscellaneous-match.guard", "defaultImport": null, "namedImports": [{"name": "participationMiscellaneousMatchGuard", "alias": null}], "namespaceImport": null, "fullText": "import { participationMiscellaneousMatchGuard } from './guards/participation-miscellaneous-match.guard';"}, {"type": "named", "moduleSpecifier": "./guards/section-application-miscellaneous-match.guard", "defaultImport": null, "namedImports": [{"name": "sectionApplicationMiscellaneousMatchGuard", "alias": null}], "namespaceImport": null, "fullText": "import { sectionApplicationMiscellaneousMatchGuard } from './guards/section-application-miscellaneous-match.guard';"}, {"type": "named", "moduleSpecifier": "./guards/section-invitation-miscellaneous-match.guard", "defaultImport": null, "namedImports": [{"name": "sectionInvitationMiscellaneousMatchGuard", "alias": null}], "namespaceImport": null, "fullText": "import { sectionInvitationMiscellaneousMatchGuard } from './guards/section-invitation-miscellaneous-match.guard';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: '',\n    loadComponent: () => import('./components/financing-details-miscellaneous/financing-details-miscellaneous.component').then(m => m.FinancingDetailsMiscellaneousComponent),\n    canActivate: [financingDetailsMiscellaneousActivateGuard],\n    children: [\n      {\n        path: FinancingDetailsPath.MY_PARTICIPATION,\n        loadComponent: () => import('./components/participation-miscellaneous/participation-miscellaneous.component').then(m => m.ParticipationMiscellaneousComponent),\n        canMatch: [participationMiscellaneousMatchGuard],\n      },\n      {\n        path: FinancingDetailsPath.MY_PARTICIPATION,\n        loadComponent: () => import('@fincloud/neoshare/business-case').then(m => m.SectionApplicationComponent),\n        canMatch: [sectionApplicationMiscellaneousMatchGuard],\n      },\n      {\n        path: FinancingDetailsPath.MY_PARTICIPATION,\n        loadComponent: () => import('@fincloud/neoshare/business-case').then(m => m.SectionInvitationComponent),\n        canMatch: [sectionInvitationMiscellaneousMatchGuard],\n      },\n    ],\n  },\n  {\n    path: '**',\n    canActivate: [participationRouteGuard],\n    children: [],\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./components/financing-details-miscellaneous/financing-details-miscellaneous.component').then(m => m.FinancingDetailsMiscellaneousComponent),\n    canActivate: [financingDetailsMiscellaneousActivateGuard],\n    children: [\n      {\n        path: FinancingDetailsPath.MY_PARTICIPATION,\n        loadComponent: () => import('./components/participation-miscellaneous/participation-miscellaneous.component').then(m => m.ParticipationMiscellaneousComponent),\n        canMatch: [participationMiscellaneousMatchGuard],\n      },\n      {\n        path: FinancingDetailsPath.MY_PARTICIPATION,\n        loadComponent: () => import('@fincloud/neoshare/business-case').then(m => m.SectionApplicationComponent),\n        canMatch: [sectionApplicationMiscellaneousMatchGuard],\n      },\n      {\n        path: FinancingDetailsPath.MY_PARTICIPATION,\n        loadComponent: () => import('@fincloud/neoshare/business-case').then(m => m.SectionInvitationComponent),\n        canMatch: [sectionInvitationMiscellaneousMatchGuard],\n      },\n    ],\n  },\n  {\n    path: '**',\n    canActivate: [participationRouteGuard],\n    children: [],\n  },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/business-case-management/business-case-management.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "./business-case-management-routing.module", "defaultImport": null, "namedImports": [{"name": "BusinessCaseManagementRoutingModule", "alias": null}], "namespaceImport": null, "fullText": "import { BusinessCaseManagementRoutingModule } from './business-case-management-routing.module';"}, {"type": "named", "moduleSpecifier": "./components/business-case-management/business-case-management.component", "defaultImport": null, "namedImports": [{"name": "BusinessCaseManagementComponent", "alias": null}], "namespaceImport": null, "fullText": "import { BusinessCaseManagementComponent } from './components/business-case-management/business-case-management.component';"}, {"type": "named", "moduleSpecifier": "./components/business-case-state/business-case-state.component", "defaultImport": null, "namedImports": [{"name": "BusinessCaseStateComponent", "alias": null}], "namespaceImport": null, "fullText": "import { BusinessCaseStateComponent } from './components/business-case-state/business-case-state.component';"}, {"type": "named", "moduleSpecifier": "./components/collaboration-state-change-modal/collaboration-state-change-modal.component", "defaultImport": null, "namedImports": [{"name": "CollaborationStateChangeModalComponent", "alias": null}], "namespaceImport": null, "fullText": "import { CollaborationStateChangeModalComponent } from './components/collaboration-state-change-modal/collaboration-state-change-modal.component';"}], "hasRoutesConstant": false, "routesConstant": null, "error": null}, {"filePath": "apps/fincloud/src/app/business-case-management/business-case-management-routing.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}, {"type": "named", "moduleSpecifier": "./components/business-case-management/business-case-management.component", "defaultImport": null, "namedImports": [{"name": "BusinessCaseManagementComponent", "alias": null}], "namespaceImport": null, "fullText": "import { BusinessCaseManagementComponent } from './components/business-case-management/business-case-management.component';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  { path: '', component: BusinessCaseManagementComponent },\n]", "fullText": "const routes: Routes = [\n  { path: '', component: BusinessCaseManagementComponent },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/business-case-kpis/business-case-kpi.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/common", "defaultImport": null, "namedImports": [{"name": "C<PERSON><PERSON>cyPipe", "alias": null}, {"name": "DecimalPipe", "alias": null}, {"name": "Percent<PERSON><PERSON>e", "alias": null}], "namespaceImport": null, "fullText": "import { C<PERSON>rencyPipe, DecimalPipe, PercentPipe } from '@angular/common';"}, {"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@fincloud/state/utils", "defaultImport": null, "namedImports": [{"name": "BUSINESS_CASE_KPI_FEATURE_KEY", "alias": null}], "namespaceImport": null, "fullText": "import { BUSINESS_CASE_KPI_FEATURE_KEY } from '@fincloud/state/utils';"}, {"type": "named", "moduleSpecifier": "@ngrx/effects", "defaultImport": null, "namedImports": [{"name": "provideEffects", "alias": null}], "namespaceImport": null, "fullText": "import { provideEffects } from '@ngrx/effects';"}, {"type": "named", "moduleSpecifier": "@ngrx/store", "defaultImport": null, "namedImports": [{"name": "provideState", "alias": null}], "namespaceImport": null, "fullText": "import { provideState } from '@ngrx/store';"}, {"type": "named", "moduleSpecifier": "./+state", "defaultImport": null, "namedImports": [{"name": "BusinessCaseKpiEffects", "alias": null}, {"name": "businessCaseKpiStateReducer", "alias": null}], "namespaceImport": null, "fullText": "import { BusinessCaseKpiEffects, businessCaseKpiStateReducer } from './+state';"}, {"type": "named", "moduleSpecifier": "./+state/effects/management-summary.effects", "defaultImport": null, "namedImports": [{"name": "ManagementSummaryEffects", "alias": null}], "namespaceImport": null, "fullText": "import { ManagementSummaryEffects } from './+state/effects/management-summary.effects';"}, {"type": "named", "moduleSpecifier": "./business-case-kpi-routing.module", "defaultImport": null, "namedImports": [{"name": "BusinessCaseKpiRoutingModule", "alias": null}], "namespaceImport": null, "fullText": "import { BusinessCaseKpiRoutingModule } from './business-case-kpi-routing.module';"}], "hasRoutesConstant": false, "routesConstant": null, "error": null}, {"filePath": "apps/fincloud/src/app/business-case-kpis/business-case-kpi-routing.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}, {"type": "named", "moduleSpecifier": "./guards/business-case-kpi-list.guard.guard", "defaultImport": null, "namedImports": [{"name": "businessCaseKpisListGuard", "alias": null}], "namespaceImport": null, "fullText": "import { businessCaseKpisListGuard } from './guards/business-case-kpi-list.guard.guard';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: '',\n    loadComponent: () => import('./components/business-case-kpi/business-case-kpi.component').then(m => m.BusinessCaseKpiComponent),\n    canActivate: [businessCaseKpisListGuard],\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./components/business-case-kpi/business-case-kpi.component').then(m => m.BusinessCaseKpiComponent),\n    canActivate: [businessCaseKpisListGuard],\n  },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/business-case-inbox/business-case-inbox.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@ngrx/effects", "defaultImport": null, "namedImports": [{"name": "provideEffects", "alias": null}], "namespaceImport": null, "fullText": "import { provideEffects } from '@ngrx/effects';"}, {"type": "named", "moduleSpecifier": "@ngrx/store", "defaultImport": null, "namedImports": [{"name": "provideState", "alias": null}], "namespaceImport": null, "fullText": "import { provideState } from '@ngrx/store';"}, {"type": "named", "moduleSpecifier": "./+state", "defaultImport": null, "namedImports": [{"name": "BusinessCaseInboxDeleteDocumentEffects", "alias": null}, {"name": "BusinessCaseInboxEffects", "alias": null}, {"name": "BusinessCaseInboxEmailEffects", "alias": null}, {"name": "BusinessCaseInboxOperationsDocumentEffects", "alias": null}, {"name": "BusinessCaseInboxUploadDocumentEffects", "alias": null}, {"name": "businessCaseInboxFeature", "alias": null}], "namespaceImport": null, "fullText": "import {\n  BusinessCaseInboxDeleteDocumentEffects,\n  BusinessCaseInboxEffects,\n  BusinessCaseInboxEmailEffects,\n  BusinessCaseInboxOperationsDocumentEffects,\n  BusinessCaseInboxUploadDocumentEffects,\n  businessCaseInboxFeature,\n} from './+state';"}, {"type": "named", "moduleSpecifier": "./+state/metareducers/business-case-inbox-storage-sync.metareducer", "defaultImport": null, "namedImports": [{"name": "businessCaseInboxStorageSyncMetaReducer", "alias": null}], "namespaceImport": null, "fullText": "import { businessCaseInboxStorageSyncMetaReducer } from './+state/metareducers/business-case-inbox-storage-sync.metareducer';"}, {"type": "named", "moduleSpecifier": "./business-case-inbox-routing.module", "defaultImport": null, "namedImports": [{"name": "BusinessCaseInboxRoutingModule", "alias": null}], "namespaceImport": null, "fullText": "import { BusinessCaseInboxRoutingModule } from './business-case-inbox-routing.module';"}, {"type": "named", "moduleSpecifier": "./utils/business-case-inbox-initial-state", "defaultImport": null, "namedImports": [{"name": "BUSINESS_CASE_INBOX_INITIAL_STATE", "alias": null}], "namespaceImport": null, "fullText": "import { BUSINESS_CASE_INBOX_INITIAL_STATE } from './utils/business-case-inbox-initial-state';"}], "hasRoutesConstant": false, "routesConstant": null, "error": null}, {"filePath": "apps/fincloud/src/app/business-case-inbox/business-case-inbox-routing.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}, {"type": "named", "moduleSpecifier": "./guards/business-case-inbox-resolver", "defaultImport": null, "namedImports": [{"name": "businessCaseInboxResolver", "alias": null}], "namespaceImport": null, "fullText": "import { businessCaseInboxResolver } from './guards/business-case-inbox-resolver';"}, {"type": "named", "moduleSpecifier": "./guards/close-document-classification-socket-connection.guard", "defaultImport": null, "namedImports": [{"name": "closeDocumentClassificationSocketConnectionGuard", "alias": null}], "namespaceImport": null, "fullText": "import { closeDocumentClassificationSocketConnectionGuard } from './guards/close-document-classification-socket-connection.guard';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: '',\n    loadComponent: () => import('./components/business-case-inbox-full-screen/business-case-inbox-full-screen.component').then(m => m.BusinessCaseInboxFullScreenComponent),\n    resolve: [businessCaseInboxResolver],\n    canDeactivate: [closeDocumentClassificationSocketConnectionGuard],\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./components/business-case-inbox-full-screen/business-case-inbox-full-screen.component').then(m => m.BusinessCaseInboxFullScreenComponent),\n    resolve: [businessCaseInboxResolver],\n    canDeactivate: [closeDocumentClassificationSocketConnectionGuard],\n  },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/business-case-data-room/business-case-data-room.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@fincloud/state/folder-structure", "defaultImport": null, "namedImports": [{"name": "FolderStructureAddDocumentEffects", "alias": null}, {"name": "FolderStructureAddFolderEffects", "alias": null}, {"name": "FolderStructureDeleteFolderEffects", "alias": null}, {"name": "FolderStructureMoveDocumentEffects", "alias": null}, {"name": "FolderStructureMoveFolderEffects", "alias": null}, {"name": "FolderStructureRenameFolderEffects", "alias": null}, {"name": "folderStructureFeature", "alias": null}], "namespaceImport": null, "fullText": "import {\n  FolderStructureAddDocumentEffects,\n  FolderStructureAddFolderEffects,\n  FolderStructureDeleteFolderEffects,\n  FolderStructureMoveDocumentEffects,\n  FolderStructureMoveFolderEffects,\n  FolderStructureRenameFolderEffects,\n  folderStructureFeature,\n} from '@fincloud/state/folder-structure';"}, {"type": "named", "moduleSpecifier": "@fincloud/state/key-information-extraction", "defaultImport": null, "namedImports": [{"name": "KeyInformationExtractionEffects", "alias": null}, {"name": "keyInformationExtractionFeature", "alias": null}], "namespaceImport": null, "fullText": "import {\n  KeyInformationExtractionEffects,\n  keyInformationExtractionFeature,\n} from '@fincloud/state/key-information-extraction';"}, {"type": "named", "moduleSpecifier": "@fincloud/ui/modal", "defaultImport": null, "namedImports": [{"name": "FIN_MODAL_DEFAULT_OPTIONS", "alias": null}, {"name": "FIN_MODAL_REF_PROVIDER", "alias": null}, {"name": "FinModalService", "alias": null}], "namespaceImport": null, "fullText": "import {\n  FIN_MODAL_DEFAULT_OPTIONS,\n  FIN_MODAL_REF_PROVIDER,\n  FinModalService,\n} from '@fincloud/ui/modal';"}, {"type": "named", "moduleSpecifier": "@ngrx/effects", "defaultImport": null, "namedImports": [{"name": "provideEffects", "alias": null}], "namespaceImport": null, "fullText": "import { provideEffects } from '@ngrx/effects';"}, {"type": "named", "moduleSpecifier": "@ngrx/store", "defaultImport": null, "namedImports": [{"name": "provideState", "alias": null}], "namespaceImport": null, "fullText": "import { provideState } from '@ngrx/store';"}, {"type": "named", "moduleSpecifier": "./+state", "defaultImport": null, "namedImports": [{"name": "BusinessCaseDataRoomEffects", "alias": null}, {"name": "businessCaseDataRoomFiltersFeature", "alias": null}], "namespaceImport": null, "fullText": "import {\n  BusinessCaseDataRoomEffects,\n  businessCaseDataRoomFiltersFeature,\n} from './+state';"}, {"type": "named", "moduleSpecifier": "./business-case-data-room-routing.module", "defaultImport": null, "namedImports": [{"name": "BusinessCaseDataRoomRoutingModule", "alias": null}], "namespaceImport": null, "fullText": "import { BusinessCaseDataRoomRoutingModule } from './business-case-data-room-routing.module';"}], "hasRoutesConstant": false, "routesConstant": null, "error": null}, {"filePath": "apps/fincloud/src/app/business-case-data-room/business-case-data-room-routing.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}, {"type": "named", "moduleSpecifier": "@fincloud/neoshare/business-case", "defaultImport": null, "namedImports": [{"name": "financingStructureActivateGuard", "alias": null}], "namespaceImport": null, "fullText": "import { financingStructureActivateGuard } from '@fincloud/neoshare/business-case';"}, {"type": "named", "moduleSpecifier": "@fincloud/neoshare/neogpt-chat", "defaultImport": null, "namedImports": [{"name": "neogptChatActivateGuard", "alias": null}, {"name": "neogptChatDeactivateGuard", "alias": null}], "namespaceImport": null, "fullText": "import {\n  neogptChatActivateGuard,\n  neogptChatDeactivateGuard,\n} from '@fincloud/neoshare/neogpt-chat';"}, {"type": "named", "moduleSpecifier": "@fincloud/types/enums", "defaultImport": null, "namedImports": [{"name": "NeoGptActiveSession", "alias": null}], "namespaceImport": null, "fullText": "import { NeoGptActiveSession } from '@fincloud/types/enums';"}, {"type": "named", "moduleSpecifier": "./guards/access-linked-comapny.guard", "defaultImport": null, "namedImports": [{"name": "accessLinkedCompanyGuard", "alias": null}], "namespaceImport": null, "fullText": "import { accessLinkedCompanyGuard } from './guards/access-linked-comapny.guard';"}, {"type": "named", "moduleSpecifier": "./guards/load-company-data-room-folder-structures.guard", "defaultImport": null, "namedImports": [{"name": "loadCompanyDataRoomFolderStructuresGuard", "alias": null}], "namespaceImport": null, "fullText": "import { loadCompanyDataRoomFolderStructuresGuard } from './guards/load-company-data-room-folder-structures.guard';"}, {"type": "named", "moduleSpecifier": "./guards/load-data-room-folder-structures.guard", "defaultImport": null, "namedImports": [{"name": "loadDataRoomFolderStructuresGuard", "alias": null}], "namespaceImport": null, "fullText": "import { loadDataRoomFolderStructuresGuard } from './guards/load-data-room-folder-structures.guard';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: '',\n    loadComponent: () => import('./components/business-case-data-room-tabs/business-case-data-room-tabs.component').then(m => m.BusinessCaseDataRoomTabsComponent),\n    children: [\n      {\n        path: '',\n        redirectTo: 'case',\n        pathMatch: 'full',\n      },\n      {\n        path: 'case',\n        loadComponent: () => import('./components/business-case-data-room/business-case-data-room.component').then(m => m.BusinessCaseDataRoomComponent),\n        canActivate: [\n          neogptChatActivateGuard,\n          loadDataRoomFolderStructuresGuard,\n          financingStructureActivateGuard,\n        ],\n        canDeactivate: [neogptChatDeactivateGuard],\n        data: { activeSession: NeoGptActiveSession.DATA_ROOM },\n      },\n      {\n        path: 'company',\n        loadComponent: () => import('./components/business-case-data-room-tabs/business-case-data-room-tabs.component').then(m => m.BusinessCaseDataRoomTabsComponent),\n        canActivate: [\n          accessLinkedCompanyGuard,\n          loadCompanyDataRoomFolderStructuresGuard,\n        ],\n      },\n    ],\n  },\n  {\n    path: 'inbox-document',\n    loadChildren: () =>\n      import('../business-case-inbox/business-case-inbox.module').then(\n        (m) => m.BusinessCaseInboxModule,\n      ),\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./components/business-case-data-room-tabs/business-case-data-room-tabs.component').then(m => m.BusinessCaseDataRoomTabsComponent),\n    children: [\n      {\n        path: '',\n        redirectTo: 'case',\n        pathMatch: 'full',\n      },\n      {\n        path: 'case',\n        loadComponent: () => import('./components/business-case-data-room/business-case-data-room.component').then(m => m.BusinessCaseDataRoomComponent),\n        canActivate: [\n          neogptChatActivateGuard,\n          loadDataRoomFolderStructuresGuard,\n          financingStructureActivateGuard,\n        ],\n        canDeactivate: [neogptChatDeactivateGuard],\n        data: { activeSession: NeoGptActiveSession.DATA_ROOM },\n      },\n      {\n        path: 'company',\n        loadComponent: () => import('./components/business-case-data-room-tabs/business-case-data-room-tabs.component').then(m => m.BusinessCaseDataRoomTabsComponent),\n        canActivate: [\n          accessLinkedCompanyGuard,\n          loadCompanyDataRoomFolderStructuresGuard,\n        ],\n      },\n    ],\n  },\n  {\n    path: 'inbox-document',\n    loadChildren: () =>\n      import('../business-case-inbox/business-case-inbox.module').then(\n        (m) => m.BusinessCaseInboxModule,\n      ),\n  },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/business-case-dashboard/business-case-dashboard.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/common", "defaultImport": null, "namedImports": [{"name": "C<PERSON><PERSON>cyPipe", "alias": null}, {"name": "DatePipe", "alias": null}, {"name": "DecimalPipe", "alias": null}], "namespaceImport": null, "fullText": "import { C<PERSON><PERSON>cyPipe, DatePipe, DecimalPipe } from '@angular/common';"}, {"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@fincloud/core/pipes", "defaultImport": null, "namedImports": [{"name": "InitialsPipe", "alias": null}, {"name": "RemoveTrailingZerosPipe", "alias": null}], "namespaceImport": null, "fullText": "import { InitialsPipe, RemoveTrailingZerosPipe } from '@fincloud/core/pipes';"}, {"type": "named", "moduleSpecifier": "@fincloud/state/business-case-real-estate", "defaultImport": null, "namedImports": [{"name": "StateLibFinancingStructureEffects", "alias": null}, {"name": "businessCaseRealEstateFeature", "alias": null}], "namespaceImport": null, "fullText": "import {\n  StateLibFinancingStructureEffects,\n  businessCaseRealEstateFeature,\n} from '@fincloud/state/business-case-real-estate';"}, {"type": "named", "moduleSpecifier": "@fincloud/state/customer", "defaultImport": null, "namedImports": [{"name": "StateLibCustomerEffects", "alias": null}], "namespaceImport": null, "fullText": "import { StateLibCustomerEffects } from '@fincloud/state/customer';"}, {"type": "named", "moduleSpecifier": "@fincloud/state/document", "defaultImport": null, "namedImports": [{"name": "StateLibDocumentEffects", "alias": null}], "namespaceImport": null, "fullText": "import { StateLibDocumentEffects } from '@fincloud/state/document';"}, {"type": "named", "moduleSpecifier": "@fincloud/state/faq", "defaultImport": null, "namedImports": [{"name": "faqFeature", "alias": null}], "namespaceImport": null, "fullText": "import { faqFeature } from '@fincloud/state/faq';"}, {"type": "named", "moduleSpecifier": "@fincloud/state/key-information-extraction", "defaultImport": null, "namedImports": [{"name": "KeyInformationExtractionControlEffects", "alias": null}, {"name": "keyInformationExtractionControlFeature", "alias": null}], "namespaceImport": null, "fullText": "import {\n  KeyInformationExtractionControlEffects,\n  keyInformationExtractionControlFeature,\n} from '@fincloud/state/key-information-extraction';"}, {"type": "named", "moduleSpecifier": "@ngrx/effects", "defaultImport": null, "namedImports": [{"name": "provideEffects", "alias": null}], "namespaceImport": null, "fullText": "import { provideEffects } from '@ngrx/effects';"}, {"type": "named", "moduleSpecifier": "@ngrx/store", "defaultImport": null, "namedImports": [{"name": "provideState", "alias": null}], "namespaceImport": null, "fullText": "import { provideState } from '@ngrx/store';"}, {"type": "named", "moduleSpecifier": "./+state", "defaultImport": null, "namedImports": [{"name": "ActivityLogsEffects", "alias": null}, {"name": "BusinessCaseEffects", "alias": null}, {"name": "CompanyEffects", "alias": null}, {"name": "CompanyPortalEffects", "alias": null}, {"name": "CriteriaEffects", "alias": null}, {"name": "CustomerEffects", "alias": null}, {"name": "DocumentsEffects", "alias": null}, {"name": "FacilityEffects", "alias": null}, {"name": "PermissionsEffects", "alias": null}, {"name": "TeaserExportEffects", "alias": null}, {"name": "UserEffects", "alias": null}], "namespaceImport": null, "fullText": "import {\n  ActivityLogsEffects,\n  BusinessCaseEffects,\n  CompanyEffects,\n  CompanyPortalEffects,\n  CriteriaEffects,\n  CustomerEffects,\n  DocumentsEffects,\n  FacilityEffects,\n  PermissionsEffects,\n  TeaserExportEffects,\n  UserEffects,\n} from './+state';"}, {"type": "named", "moduleSpecifier": "./+state/reducers", "defaultImport": null, "namedImports": [{"name": "businessCaseDashboardFeature", "alias": null}], "namespaceImport": null, "fullText": "import { businessCaseDashboardFeature } from './+state/reducers';"}, {"type": "named", "moduleSpecifier": "./business-case-dashboard-routing.module", "defaultImport": null, "namedImports": [{"name": "BusinessCaseDashboardRoutingModule", "alias": null}], "namespaceImport": null, "fullText": "import { BusinessCaseDashboardRoutingModule } from './business-case-dashboard-routing.module';"}], "hasRoutesConstant": false, "routesConstant": null, "error": null}, {"filePath": "apps/fincloud/src/app/business-case-dashboard/business-case-dashboard-routing.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "ActivatedRouteSnapshot", "alias": null}, {"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { ActivatedRouteSnapshot, Routes } from '@angular/router';"}, {"type": "named", "moduleSpecifier": "@fincloud/neoshare/guards", "defaultImport": null, "namedImports": [{"name": "faqDataResolver", "alias": null}, {"name": "restrictedCaseAccessGuard", "alias": null}], "namespaceImport": null, "fullText": "import {\n  faqDataResolver,\n  restrictedCaseAccessGuard,\n} from '@fincloud/neoshare/guards';"}, {"type": "named", "moduleSpecifier": "@fincloud/state/business-case", "defaultImport": null, "namedImports": [{"name": "selectIsBusinessCaseCorporate", "alias": null}, {"name": "selectIsBusinessCaseMiscellaneous", "alias": null}, {"name": "selectIsBusinessCaseRealEstate", "alias": null}], "namespaceImport": null, "fullText": "import {\n  selectIsBusinessCaseCorporate,\n  selectIsBusinessCaseMiscellaneous,\n  selectIsBusinessCaseRealEstate,\n} from '@fincloud/state/business-case';"}, {"type": "named", "moduleSpecifier": "@fincloud/types/enums", "defaultImport": null, "namedImports": [{"name": "FinancingDetailsPath", "alias": null}], "namespaceImport": null, "fullText": "import { FinancingDetailsPath } from '@fincloud/types/enums';"}, {"type": "named", "moduleSpecifier": "./guards/can-activate-administration.guard", "defaultImport": null, "namedImports": [{"name": "canActivateAdministrationGuard", "alias": null}], "namespaceImport": null, "fullText": "import { canActivateAdministrationGuard } from './guards/can-activate-administration.guard';"}, {"type": "named", "moduleSpecifier": "./guards/can-activate-business-case-overview.guard", "defaultImport": null, "namedImports": [{"name": "canActivateBusinessCaseOverviewGuard", "alias": null}], "namespaceImport": null, "fullText": "import { canActivateBusinessCaseOverviewGuard } from './guards/can-activate-business-case-overview.guard';"}, {"type": "named", "moduleSpecifier": "./guards/can-activate-business-case.guard", "defaultImport": null, "namedImports": [{"name": "canActivateBusinessCaseGuard", "alias": null}], "namespaceImport": null, "fullText": "import { canActivateBusinessCaseGuard } from './guards/can-activate-business-case.guard';"}, {"type": "named", "moduleSpecifier": "./guards/can-activate-case-to-case-routing.guard", "defaultImport": null, "namedImports": [{"name": "CanActivateCaseToCaseRoutingGuard", "alias": null}], "namespaceImport": null, "fullText": "import { CanActivateCaseToCaseRoutingGuard } from './guards/can-activate-case-to-case-routing.guard';"}, {"type": "named", "moduleSpecifier": "./guards/can-match-financing-structure-factory.guard", "defaultImport": null, "namedImports": [{"name": "canMatchFinancingStructureFactory", "alias": null}], "namespaceImport": null, "fullText": "import { canMatchFinancingStructureFactory } from './guards/can-match-financing-structure-factory.guard';"}, {"type": "named", "moduleSpecifier": "./guards/duplicate-business-case.guard", "defaultImport": null, "namedImports": [{"name": "duplicateBusinessCaseAccessGuard", "alias": null}], "namespaceImport": null, "fullText": "import { duplicateBusinessCaseAccessGuard } from './guards/duplicate-business-case.guard';"}, {"type": "named", "moduleSpecifier": "./guards/load-case-fields-access.guard", "defaultImport": null, "namedImports": [{"name": "loadCaseFieldsAccessGuard", "alias": null}], "namespaceImport": null, "fullText": "import { loadCaseFieldsAccessGuard } from './guards/load-case-fields-access.guard';"}, {"type": "named", "moduleSpecifier": "./guards/redirect-non-match.guard", "defaultImport": null, "namedImports": [{"name": "redirectOnNonMatchingGuard", "alias": null}], "namespaceImport": null, "fullText": "import { redirectOnNonMatchingGuard } from './guards/redirect-non-match.guard';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: ':id',\n    loadComponent: () => import('./components/business-case-dashboard/business-case-dashboard.component').then(m => m.BusinessCaseDashboardComponent),\n    canActivate: [canActivateBusinessCaseGuard, loadCaseFieldsAccessGuard],\n    children: [\n      {\n        path: '',\n        redirectTo: 'overview',\n        pathMatch: 'full',\n      },\n      {\n        path: 'overview',\n        loadComponent: () => import('./components/business-case-overview/business-case-overview.component').then(m => m.BusinessCaseOverviewComponent),\n        canActivate: [canActivateBusinessCaseOverviewGuard],\n        children: [\n          {\n            path: '',\n            redirectTo: 'general',\n            pathMatch: 'full',\n          },\n          {\n            path: 'kpi-settings',\n            loadChildren: () =>\n              import('../business-case-kpis/business-case-kpi.module').then(\n                (m) => m.BusinessCaseKpiModule,\n              ),\n          },\n          {\n            path: 'general',\n            loadComponent: () => import('./components/business-case-overview-general/business-case-overview-general.component').then(m => m.BusinessCaseOverviewGeneralComponent),\n            resolve: [faqDataResolver],\n          },\n        ],\n      },\n      {\n        path: FinancingDetailsPath.FINANCING_DETAILS,\n        canMatch: [\n          () =>\n            canMatchFinancingStructureFactory(selectIsBusinessCaseCorporate),\n        ],\n        loadChildren: () =>\n          import(\n            '../business-case-corporate-fs/business-case-corporate-financing-structure.module'\n          ).then((m) => m.BusinessCaseCorporateFinancingStructureModule),\n      },\n      {\n        path: FinancingDetailsPath.FINANCING_DETAILS,\n        canMatch: [\n          () =>\n            canMatchFinancingStructureFactory(\n              selectIsBusinessCaseMiscellaneous,\n            ),\n        ],\n        loadChildren: () =>\n          import(\n            '../business-case-miscellaneous-fs/business-case-miscellaneous-financing-structure.module'\n          ).then((m) => m.BusinessCaseMiscellaneousFinancingStructureModule),\n      },\n      {\n        path: FinancingDetailsPath.FINANCING_DETAILS,\n        canMatch: [\n          () =>\n            canMatchFinancingStructureFactory(selectIsBusinessCaseRealEstate),\n        ],\n        loadChildren: () =>\n          import(\n            '../business-case-real-estate-fs/business-case-real-estate-financing-structure.module'\n          ).then((m) => m.BusinessCaseRealEstateFinancingStructureModule),\n      },\n      {\n        path: 'data-room',\n        loadChildren: () =>\n          import(\n            '../business-case-data-room/business-case-data-room.module'\n          ).then((m) => m.BusinessCaseDataRoomModule),\n      },\n      {\n        path: 'management',\n        canMatch: [canActivateAdministrationGuard],\n        loadChildren: () =>\n          import(\n            '../business-case-administration/business-case-administration.module'\n          ).then((m) => m.BusinessCaseAdministrationModule),\n      },\n      {\n        path: 'participant-status',\n        loadComponent: () => import('@fincloud/neoshare/business-case').then(m => m.BusinessCaseParticipantRolesComponent),\n      },\n      {\n        path: 'apply',\n        loadComponent: () => import('@fincloud/neoshare/business-case').then(m => m.BusinessCaseApplyConfirmationModalComponent),\n      },\n      {\n        path: 'collaboration',\n        loadChildren: () =>\n          import(\n            '../business-case-collaboration/business-case-collaboration.module'\n          ).then((m) => m.BusinessCaseCollaborationModule),\n      },\n    ],\n  },\n  {\n    canActivate: [\n      CanActivateCaseToCaseRoutingGuard,\n      canActivateBusinessCaseGuard,\n    ],\n    path: ':id/chat',\n    loadChildren: () => import('../chat/chat.module').then((m) => m.ChatModule),\n  },\n  {\n    canActivate: [\n      canActivateBusinessCaseGuard,\n      duplicateBusinessCaseAccessGuard,\n    ],\n    path: ':id/duplicate-case',\n    loadChildren: () =>\n      import('../duplicate-business-case/duplicate-business-case.module').then(\n        (m) => m.DuplicateBusinessCaseModule,\n      ),\n  },\n  {\n    path: ':id/restricted-access',\n    canActivate: [restrictedCaseAccessGuard],\n    loadComponent: () => import('./components/restricted-case-access/restricted-case-access.component').then(m => m.RestrictedCaseAccessComponent),\n  },\n  // Usually on reload, were data is still fetching , all canMatch will probably\n  // return false, since data is not loaded and they can't wait ror it,\n  // we need to redirect to same page and pass from canActivate to wait fot the data\n  {\n    path: '**',\n    canActivate: [\n      (route: ActivatedRouteSnapshot) => redirectOnNonMatchingGuard(route),\n    ],\n    children: [],\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: ':id',\n    loadComponent: () => import('./components/business-case-dashboard/business-case-dashboard.component').then(m => m.BusinessCaseDashboardComponent),\n    canActivate: [canActivateBusinessCaseGuard, loadCaseFieldsAccessGuard],\n    children: [\n      {\n        path: '',\n        redirectTo: 'overview',\n        pathMatch: 'full',\n      },\n      {\n        path: 'overview',\n        loadComponent: () => import('./components/business-case-overview/business-case-overview.component').then(m => m.BusinessCaseOverviewComponent),\n        canActivate: [canActivateBusinessCaseOverviewGuard],\n        children: [\n          {\n            path: '',\n            redirectTo: 'general',\n            pathMatch: 'full',\n          },\n          {\n            path: 'kpi-settings',\n            loadChildren: () =>\n              import('../business-case-kpis/business-case-kpi.module').then(\n                (m) => m.BusinessCaseKpiModule,\n              ),\n          },\n          {\n            path: 'general',\n            loadComponent: () => import('./components/business-case-overview-general/business-case-overview-general.component').then(m => m.BusinessCaseOverviewGeneralComponent),\n            resolve: [faqDataResolver],\n          },\n        ],\n      },\n      {\n        path: FinancingDetailsPath.FINANCING_DETAILS,\n        canMatch: [\n          () =>\n            canMatchFinancingStructureFactory(selectIsBusinessCaseCorporate),\n        ],\n        loadChildren: () =>\n          import(\n            '../business-case-corporate-fs/business-case-corporate-financing-structure.module'\n          ).then((m) => m.BusinessCaseCorporateFinancingStructureModule),\n      },\n      {\n        path: FinancingDetailsPath.FINANCING_DETAILS,\n        canMatch: [\n          () =>\n            canMatchFinancingStructureFactory(\n              selectIsBusinessCaseMiscellaneous,\n            ),\n        ],\n        loadChildren: () =>\n          import(\n            '../business-case-miscellaneous-fs/business-case-miscellaneous-financing-structure.module'\n          ).then((m) => m.BusinessCaseMiscellaneousFinancingStructureModule),\n      },\n      {\n        path: FinancingDetailsPath.FINANCING_DETAILS,\n        canMatch: [\n          () =>\n            canMatchFinancingStructureFactory(selectIsBusinessCaseRealEstate),\n        ],\n        loadChildren: () =>\n          import(\n            '../business-case-real-estate-fs/business-case-real-estate-financing-structure.module'\n          ).then((m) => m.BusinessCaseRealEstateFinancingStructureModule),\n      },\n      {\n        path: 'data-room',\n        loadChildren: () =>\n          import(\n            '../business-case-data-room/business-case-data-room.module'\n          ).then((m) => m.BusinessCaseDataRoomModule),\n      },\n      {\n        path: 'management',\n        canMatch: [canActivateAdministrationGuard],\n        loadChildren: () =>\n          import(\n            '../business-case-administration/business-case-administration.module'\n          ).then((m) => m.BusinessCaseAdministrationModule),\n      },\n      {\n        path: 'participant-status',\n        loadComponent: () => import('@fincloud/neoshare/business-case').then(m => m.BusinessCaseParticipantRolesComponent),\n      },\n      {\n        path: 'apply',\n        loadComponent: () => import('@fincloud/neoshare/business-case').then(m => m.BusinessCaseApplyConfirmationModalComponent),\n      },\n      {\n        path: 'collaboration',\n        loadChildren: () =>\n          import(\n            '../business-case-collaboration/business-case-collaboration.module'\n          ).then((m) => m.BusinessCaseCollaborationModule),\n      },\n    ],\n  },\n  {\n    canActivate: [\n      CanActivateCaseToCaseRoutingGuard,\n      canActivateBusinessCaseGuard,\n    ],\n    path: ':id/chat',\n    loadChildren: () => import('../chat/chat.module').then((m) => m.ChatModule),\n  },\n  {\n    canActivate: [\n      canActivateBusinessCaseGuard,\n      duplicateBusinessCaseAccessGuard,\n    ],\n    path: ':id/duplicate-case',\n    loadChildren: () =>\n      import('../duplicate-business-case/duplicate-business-case.module').then(\n        (m) => m.DuplicateBusinessCaseModule,\n      ),\n  },\n  {\n    path: ':id/restricted-access',\n    canActivate: [restrictedCaseAccessGuard],\n    loadComponent: () => import('./components/restricted-case-access/restricted-case-access.component').then(m => m.RestrictedCaseAccessComponent),\n  },\n  // Usually on reload, were data is still fetching , all canMatch will probably\n  // return false, since data is not loaded and they can't wait ror it,\n  // we need to redirect to same page and pass from canActivate to wait fot the data\n  {\n    path: '**',\n    canActivate: [\n      (route: ActivatedRouteSnapshot) => redirectOnNonMatchingGuard(route),\n    ],\n    children: [],\n  },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/business-case-corporate-fs/business-case-corporate-financing-structure.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "./business-case-corporate-financing-structure-routing.module", "defaultImport": null, "namedImports": [{"name": "BusinessCaseCorporateFinancingStructureRoutingModule", "alias": null}], "namespaceImport": null, "fullText": "import { BusinessCaseCorporateFinancingStructureRoutingModule } from './business-case-corporate-financing-structure-routing.module';"}], "hasRoutesConstant": false, "routesConstant": null, "error": null}, {"filePath": "apps/fincloud/src/app/business-case-corporate-fs/business-case-corporate-financing-structure-routing.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}, {"type": "named", "moduleSpecifier": "@fincloud/neoshare/guards", "defaultImport": null, "namedImports": [{"name": "participationRouteGuard", "alias": null}], "namespaceImport": null, "fullText": "import { participationRouteGuard } from '@fincloud/neoshare/guards';"}, {"type": "named", "moduleSpecifier": "@fincloud/types/enums", "defaultImport": null, "namedImports": [{"name": "FinancingDetailsPath", "alias": null}, {"name": "FinancingDetailsSubPage", "alias": null}], "namespaceImport": null, "fullText": "import {\n  FinancingDetailsPath,\n  FinancingDetailsSubPage,\n} from '@fincloud/types/enums';"}, {"type": "named", "moduleSpecifier": "./guards/corporate-financing-sructure-resolver", "defaultImport": null, "namedImports": [{"name": "corporateFinancingStructureResolver", "alias": null}], "namespaceImport": null, "fullText": "import { corporateFinancingStructureResolver } from './guards/corporate-financing-sructure-resolver';"}, {"type": "named", "moduleSpecifier": "./guards/financing-details-corporate-activate.guard", "defaultImport": null, "namedImports": [{"name": "financingDetailsCorporateActivateGuard", "alias": null}], "namespaceImport": null, "fullText": "import { financingDetailsCorporateActivateGuard } from './guards/financing-details-corporate-activate.guard';"}, {"type": "named", "moduleSpecifier": "./guards/financing-details-corporate-deactivate.guard", "defaultImport": null, "namedImports": [{"name": "financingDetailsCorporateDeactivateGuard", "alias": null}], "namespaceImport": null, "fullText": "import { financingDetailsCorporateDeactivateGuard } from './guards/financing-details-corporate-deactivate.guard';"}, {"type": "named", "moduleSpecifier": "./guards/financing-structure-corporate-activate.guard", "defaultImport": null, "namedImports": [{"name": "financingStructureCorporateActivateGuard", "alias": null}], "namespaceImport": null, "fullText": "import { financingStructureCorporateActivateGuard } from './guards/financing-structure-corporate-activate.guard';"}, {"type": "named", "moduleSpecifier": "./guards/participation-corporate-match.guard", "defaultImport": null, "namedImports": [{"name": "participationCorporateMatchGuard", "alias": null}], "namespaceImport": null, "fullText": "import { participationCorporateMatchGuard } from './guards/participation-corporate-match.guard';"}, {"type": "named", "moduleSpecifier": "./guards/section-application-corporate-match.guard", "defaultImport": null, "namedImports": [{"name": "sectionApplicationCorporateMatchGuard", "alias": null}], "namespaceImport": null, "fullText": "import { sectionApplicationCorporateMatchGuard } from './guards/section-application-corporate-match.guard';"}, {"type": "named", "moduleSpecifier": "./guards/section-invitation-corporate-match.guard", "defaultImport": null, "namedImports": [{"name": "sectionInvitationCorporateMatchGuard", "alias": null}], "namespaceImport": null, "fullText": "import { sectionInvitationCorporateMatchGuard } from './guards/section-invitation-corporate-match.guard';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: '',\n    loadComponent: () => import('./components/financing-details-corporate/financing-details-corporate.component').then(m => m.FinancingDetailsCorporateComponent),\n    canActivate: [financingDetailsCorporateActivateGuard],\n    canDeactivate: [financingDetailsCorporateDeactivateGuard],\n    children: [\n      {\n        path: FinancingDetailsPath.FINANCING_STRUCTURE,\n        loadComponent: () => import('./components/financing-structure/financing-structure.component').then(m => m.FinancingStructureComponent),\n        canActivate: [financingStructureCorporateActivateGuard],\n        data: {\n          activePath: FinancingDetailsSubPage.FINANCING_STRUCTURE,\n        },\n        resolve: [corporateFinancingStructureResolver],\n      },\n      {\n        path: FinancingDetailsPath.MY_PARTICIPATION,\n        loadComponent: () => import('./components/participation-corporate/participation-corporate.component').then(m => m.ParticipationCorporateComponent),\n        canMatch: [participationCorporateMatchGuard],\n        data: { activePath: FinancingDetailsSubPage.MY_PARTICIPATION },\n      },\n      {\n        path: FinancingDetailsPath.MY_PARTICIPATION,\n        loadComponent: () => import('@fincloud/neoshare/business-case').then(m => m.SectionApplicationComponent),\n        canMatch: [sectionApplicationCorporateMatchGuard],\n        data: { activePath: FinancingDetailsSubPage.MY_PARTICIPATION },\n      },\n      {\n        path: FinancingDetailsPath.MY_PARTICIPATION,\n        loadComponent: () => import('@fincloud/neoshare/business-case').then(m => m.SectionInvitationComponent),\n        canMatch: [sectionInvitationCorporateMatchGuard],\n        data: { activePath: FinancingDetailsSubPage.MY_PARTICIPATION },\n      },\n    ],\n  },\n  {\n    path: '**',\n    canActivate: [participationRouteGuard],\n    children: [],\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./components/financing-details-corporate/financing-details-corporate.component').then(m => m.FinancingDetailsCorporateComponent),\n    canActivate: [financingDetailsCorporateActivateGuard],\n    canDeactivate: [financingDetailsCorporateDeactivateGuard],\n    children: [\n      {\n        path: FinancingDetailsPath.FINANCING_STRUCTURE,\n        loadComponent: () => import('./components/financing-structure/financing-structure.component').then(m => m.FinancingStructureComponent),\n        canActivate: [financingStructureCorporateActivateGuard],\n        data: {\n          activePath: FinancingDetailsSubPage.FINANCING_STRUCTURE,\n        },\n        resolve: [corporateFinancingStructureResolver],\n      },\n      {\n        path: FinancingDetailsPath.MY_PARTICIPATION,\n        loadComponent: () => import('./components/participation-corporate/participation-corporate.component').then(m => m.ParticipationCorporateComponent),\n        canMatch: [participationCorporateMatchGuard],\n        data: { activePath: FinancingDetailsSubPage.MY_PARTICIPATION },\n      },\n      {\n        path: FinancingDetailsPath.MY_PARTICIPATION,\n        loadComponent: () => import('@fincloud/neoshare/business-case').then(m => m.SectionApplicationComponent),\n        canMatch: [sectionApplicationCorporateMatchGuard],\n        data: { activePath: FinancingDetailsSubPage.MY_PARTICIPATION },\n      },\n      {\n        path: FinancingDetailsPath.MY_PARTICIPATION,\n        loadComponent: () => import('@fincloud/neoshare/business-case').then(m => m.SectionInvitationComponent),\n        canMatch: [sectionInvitationCorporateMatchGuard],\n        data: { activePath: FinancingDetailsSubPage.MY_PARTICIPATION },\n      },\n    ],\n  },\n  {\n    path: '**',\n    canActivate: [participationRouteGuard],\n    children: [],\n  },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/business-case-collaboration/business-case-collaboration.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@ngrx/effects", "defaultImport": null, "namedImports": [{"name": "provideEffects", "alias": null}], "namespaceImport": null, "fullText": "import { provideEffects } from '@ngrx/effects';"}, {"type": "named", "moduleSpecifier": "./+state/effects/business-case-collaboration.effects", "defaultImport": null, "namedImports": [{"name": "BusinessCaseCollaborationEffects", "alias": null}], "namespaceImport": null, "fullText": "import { BusinessCaseCollaborationEffects } from './+state/effects/business-case-collaboration.effects';"}, {"type": "named", "moduleSpecifier": "./business-case-collaboration-routing.module", "defaultImport": null, "namedImports": [{"name": "BusinessCaseCollaborationRoutingModule", "alias": null}], "namespaceImport": null, "fullText": "import { BusinessCaseCollaborationRoutingModule } from './business-case-collaboration-routing.module';"}], "hasRoutesConstant": false, "routesConstant": null, "error": null}, {"filePath": "apps/fincloud/src/app/business-case-collaboration/business-case-collaboration-routing.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}, {"type": "named", "moduleSpecifier": "./guards/can-access-my-partner.guard", "defaultImport": null, "namedImports": [{"name": "canAccessMyPartnerGuard", "alias": null}], "namespaceImport": null, "fullText": "import { canAccessMyPartnerGuard } from './guards/can-access-my-partner.guard';"}, {"type": "named", "moduleSpecifier": "./guards/private-collaboration-invitations-applications.guard", "defaultImport": null, "namedImports": [{"name": "privateCollaborationInvitationsApplicationsGuard", "alias": null}], "namespaceImport": null, "fullText": "import { privateCollaborationInvitationsApplicationsGuard } from './guards/private-collaboration-invitations-applications.guard';"}, {"type": "named", "moduleSpecifier": "./guards/private-collaboration.guard", "defaultImport": null, "namedImports": [{"name": "privateCollaborationGuard", "alias": null}], "namespaceImport": null, "fullText": "import { privateCollaborationGuard } from './guards/private-collaboration.guard';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: '',\n    loadComponent: () => import('./components/collaboration/business-case-collaboration.component').then(m => m.BusinessCaseCollaborationComponent),\n    canActivate: [privateCollaborationGuard],\n    children: [\n      {\n        path: '',\n        redirectTo: 'my-partners',\n        pathMatch: 'full',\n      },\n      {\n        path: 'my-partners',\n        loadComponent: () => import('./components/collaboration-my-partners/collaboration-my-partners.component').then(m => m.CollaborationMyPartnersComponent),\n        canActivate: [canAccessMyPartnerGuard],\n        children: [\n          {\n            path: ':participantKey',\n            loadComponent: () => import('@fincloud/neoshare/business-case').then(m => m.BusinessCaseParticipantRolesComponent),\n          },\n        ],\n      },\n      {\n        path: 'invitations-applications',\n        loadComponent: () => import('./components/collaboration-applications-invitations/collaboration-applications-invitations.component').then(m => m.CollaborationApplicationsInvitationsComponent),\n        canActivate: [privateCollaborationInvitationsApplicationsGuard],\n      },\n    ],\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./components/collaboration/business-case-collaboration.component').then(m => m.BusinessCaseCollaborationComponent),\n    canActivate: [privateCollaborationGuard],\n    children: [\n      {\n        path: '',\n        redirectTo: 'my-partners',\n        pathMatch: 'full',\n      },\n      {\n        path: 'my-partners',\n        loadComponent: () => import('./components/collaboration-my-partners/collaboration-my-partners.component').then(m => m.CollaborationMyPartnersComponent),\n        canActivate: [canAccessMyPartnerGuard],\n        children: [\n          {\n            path: ':participantKey',\n            loadComponent: () => import('@fincloud/neoshare/business-case').then(m => m.BusinessCaseParticipantRolesComponent),\n          },\n        ],\n      },\n      {\n        path: 'invitations-applications',\n        loadComponent: () => import('./components/collaboration-applications-invitations/collaboration-applications-invitations.component').then(m => m.CollaborationApplicationsInvitationsComponent),\n        canActivate: [privateCollaborationInvitationsApplicationsGuard],\n      },\n    ],\n  },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/business-case-administration/business-case-administration.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@fincloud/state/faq", "defaultImport": null, "namedImports": [{"name": "faqFeature", "alias": null}], "namespaceImport": null, "fullText": "import { faqFeature } from '@fincloud/state/faq';"}, {"type": "named", "moduleSpecifier": "@fincloud/state/utils", "defaultImport": null, "namedImports": [{"name": "ADMINISTRATION_FEATURE_KEY", "alias": null}], "namespaceImport": null, "fullText": "import { ADMINISTRATION_FEATURE_KEY } from '@fincloud/state/utils';"}, {"type": "named", "moduleSpecifier": "@ngrx/effects", "defaultImport": null, "namedImports": [{"name": "provideEffects", "alias": null}], "namespaceImport": null, "fullText": "import { provideEffects } from '@ngrx/effects';"}, {"type": "named", "moduleSpecifier": "@ngrx/store", "defaultImport": null, "namedImports": [{"name": "provideState", "alias": null}], "namespaceImport": null, "fullText": "import { provideState } from '@ngrx/store';"}, {"type": "named", "moduleSpecifier": "./+state/effects/business-case-administration.effects", "defaultImport": null, "namedImports": [{"name": "BusinessCaseAdministrationEffects", "alias": null}], "namespaceImport": null, "fullText": "import { BusinessCaseAdministrationEffects } from './+state/effects/business-case-administration.effects';"}, {"type": "named", "moduleSpecifier": "./+state/reducers/business-case-administration.reducer", "defaultImport": null, "namedImports": [{"name": "businessCaseAdministrationReducer", "alias": null}], "namespaceImport": null, "fullText": "import { businessCaseAdministrationReducer } from './+state/reducers/business-case-administration.reducer';"}, {"type": "named", "moduleSpecifier": "./business-case-administration-routing.module", "defaultImport": null, "namedImports": [{"name": "BusinessCaseAdministrationRoutingModule", "alias": null}], "namespaceImport": null, "fullText": "import { BusinessCaseAdministrationRoutingModule } from './business-case-administration-routing.module';"}], "hasRoutesConstant": false, "routesConstant": null, "error": null}, {"filePath": "apps/fincloud/src/app/business-case-administration/business-case-administration-routing.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}, {"type": "named", "moduleSpecifier": "@fincloud/neoshare/guards", "defaultImport": null, "namedImports": [{"name": "faqDataResolver", "alias": null}], "namespaceImport": null, "fullText": "import { faqDataResolver } from '@fincloud/neoshare/guards';"}, {"type": "named", "moduleSpecifier": "./guards/administration-integrations-activate.guard", "defaultImport": null, "namedImports": [{"name": "administrationIntegrationsActivateGuard", "alias": null}], "namespaceImport": null, "fullText": "import { administrationIntegrationsActivateGuard } from './guards/administration-integrations-activate.guard';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: '',\n    loadComponent: () => import('./components/administration/administration.component').then(m => m.AdministrationComponent),\n    pathMatch: 'prefix',\n    children: [\n      {\n        path: '',\n        redirectTo: 'users',\n        pathMatch: 'full',\n      },\n      {\n        path: 'users',\n        loadComponent: () => import('./components/administration-users-page/administration-users.component').then(m => m.AdministrationUsersComponent),\n      },\n      {\n        path: 'faq',\n        loadComponent: () => import('./components/administration-faq-page/administration-faq.component').then(m => m.AdministrationFaqComponent),\n        resolve: [faqDataResolver],\n      },\n      {\n        path: 'case-settings',\n        loadComponent: () => import('./components/administration-case-settings-page/administration-case-settings.component').then(m => m.AdministrationCaseSettingsComponent),\n      },\n      {\n        path: 'integrations',\n        loadComponent: () => import('./components/administration-integrations/administration-integrations.component').then(m => m.AdministrationIntegrationsComponent),\n        canActivate: [administrationIntegrationsActivateGuard],\n      },\n    ],\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./components/administration/administration.component').then(m => m.AdministrationComponent),\n    pathMatch: 'prefix',\n    children: [\n      {\n        path: '',\n        redirectTo: 'users',\n        pathMatch: 'full',\n      },\n      {\n        path: 'users',\n        loadComponent: () => import('./components/administration-users-page/administration-users.component').then(m => m.AdministrationUsersComponent),\n      },\n      {\n        path: 'faq',\n        loadComponent: () => import('./components/administration-faq-page/administration-faq.component').then(m => m.AdministrationFaqComponent),\n        resolve: [faqDataResolver],\n      },\n      {\n        path: 'case-settings',\n        loadComponent: () => import('./components/administration-case-settings-page/administration-case-settings.component').then(m => m.AdministrationCaseSettingsComponent),\n      },\n      {\n        path: 'integrations',\n        loadComponent: () => import('./components/administration-integrations/administration-integrations.component').then(m => m.AdministrationIntegrationsComponent),\n        canActivate: [administrationIntegrationsActivateGuard],\n      },\n    ],\n  },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/apps-integration/apps-integration.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}, {"type": "named", "moduleSpecifier": "@fincloud/state/utils", "defaultImport": null, "namedImports": [{"name": "APPS_INTEGRATIONS_FEATURE_KEY", "alias": null}], "namespaceImport": null, "fullText": "import { APPS_INTEGRATIONS_FEATURE_KEY } from '@fincloud/state/utils';"}, {"type": "named", "moduleSpecifier": "@ngrx/effects", "defaultImport": null, "namedImports": [{"name": "provideEffects", "alias": null}], "namespaceImport": null, "fullText": "import { provideEffects } from '@ngrx/effects';"}, {"type": "named", "moduleSpecifier": "@ngrx/store", "defaultImport": null, "namedImports": [{"name": "provideState", "alias": null}], "namespaceImport": null, "fullText": "import { provideState } from '@ngrx/store';"}, {"type": "namespace", "moduleSpecifier": "./+state/effects", "defaultImport": null, "namedImports": [], "namespaceImport": "effects", "fullText": "import * as effects from './+state/effects';"}, {"type": "named", "moduleSpecifier": "./+state/reducers/apps-integrations.reducer", "defaultImport": null, "namedImports": [{"name": "appIntegrationsReducer", "alias": null}], "namespaceImport": null, "fullText": "import { appIntegrationsReducer } from './+state/reducers/apps-integrations.reducer';"}, {"type": "named", "moduleSpecifier": "./guards/apps-integration.guard", "defaultImport": null, "namedImports": [{"name": "appsIntegrationGuard", "alias": null}], "namespaceImport": null, "fullText": "import { appsIntegrationGuard } from './guards/apps-integration.guard';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: '',\n    canActivate: [appsIntegrationGuard],\n    loadComponent: () => import('./components/apps-integration/apps-integration.component').then(m => m.AppsIntegrationComponent),\n  },\n  {\n    path: 'dracoon-integration',\n    canActivate: [appsIntegrationGuard],\n    loadComponent: () => import('./components/dracoon-integration/dracoon-integration.component').then(m => m.DracoonIntegrationComponent),\n  },\n  {\n    path: 'nextfolder-integration',\n    canActivate: [appsIntegrationGuard],\n    loadComponent: () => import('./components/nextfolder-integration/nextfolder-integration.component').then(m => m.NextfolderIntegrationComponent),\n  },\n  {\n    path: 'core-banking-integration',\n    loadComponent: () => import('./components/core-banking-integration/core-banking-integration.component').then(m => m.CoreBankingIntegrationComponent),\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: '',\n    canActivate: [appsIntegrationGuard],\n    loadComponent: () => import('./components/apps-integration/apps-integration.component').then(m => m.AppsIntegrationComponent),\n  },\n  {\n    path: 'dracoon-integration',\n    canActivate: [appsIntegrationGuard],\n    loadComponent: () => import('./components/dracoon-integration/dracoon-integration.component').then(m => m.DracoonIntegrationComponent),\n  },\n  {\n    path: 'nextfolder-integration',\n    canActivate: [appsIntegrationGuard],\n    loadComponent: () => import('./components/nextfolder-integration/nextfolder-integration.component').then(m => m.NextfolderIntegrationComponent),\n  },\n  {\n    path: 'core-banking-integration',\n    loadComponent: () => import('./components/core-banking-integration/core-banking-integration.component').then(m => m.CoreBankingIntegrationComponent),\n  },\n];"}, "error": null}, {"filePath": "apps/fincloud/src/app/account-management/account-management.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@ngrx/effects", "defaultImport": null, "namedImports": [{"name": "provideEffects", "alias": null}], "namespaceImport": null, "fullText": "import { provideEffects } from '@ngrx/effects';"}, {"type": "named", "moduleSpecifier": "@ngrx/store", "defaultImport": null, "namedImports": [{"name": "provideState", "alias": null}], "namespaceImport": null, "fullText": "import { provideState } from '@ngrx/store';"}, {"type": "named", "moduleSpecifier": "./+state/effects/account-management.effects", "defaultImport": null, "namedImports": [{"name": "AccountManagementEffects", "alias": null}], "namespaceImport": null, "fullText": "import { AccountManagementEffects } from './+state/effects/account-management.effects';"}, {"type": "named", "moduleSpecifier": "./+state/reducers/account-management.reducer", "defaultImport": null, "namedImports": [{"name": "accountManagementReducer", "alias": null}, {"name": "accountManagementStateSlice", "alias": null}, {"name": "initialState", "alias": null}], "namespaceImport": null, "fullText": "import {\n  accountManagementReducer,\n  accountManagementStateSlice,\n  initialState,\n} from './+state/reducers/account-management.reducer';"}, {"type": "named", "moduleSpecifier": "./account-management-routing.module", "defaultImport": null, "namedImports": [{"name": "AccountManagementRoutingModule", "alias": null}], "namespaceImport": null, "fullText": "import { AccountManagementRoutingModule } from './account-management-routing.module';"}], "hasRoutesConstant": false, "routesConstant": null, "error": null}, {"filePath": "apps/fincloud/src/app/account-management/account-management-routing.module.ts", "imports": [{"type": "named", "moduleSpecifier": "@angular/core", "defaultImport": null, "namedImports": [{"name": "NgModule", "alias": null}], "namespaceImport": null, "fullText": "import { NgModule } from '@angular/core';"}, {"type": "named", "moduleSpecifier": "@angular/router", "defaultImport": null, "namedImports": [{"name": "Routes", "alias": null}], "namespaceImport": null, "fullText": "import { Routes } from '@angular/router';"}, {"type": "named", "moduleSpecifier": "./guards/customer-template.guard", "defaultImport": null, "namedImports": [{"name": "customerTemplateGuard", "alias": null}], "namespaceImport": null, "fullText": "import { customerTemplateGuard } from './guards/customer-template.guard';"}, {"type": "named", "moduleSpecifier": "./guards/user-list.guard", "defaultImport": null, "namedImports": [{"name": "userListGuard", "alias": null}], "namespaceImport": null, "fullText": "import { userListGuard } from './guards/user-list.guard';"}], "hasRoutesConstant": true, "routesConstant": {"name": "routes", "constantName": "ROUTES", "content": "[\n  {\n    path: '',\n    pathMatch: 'full',\n    redirectTo: 'customers',\n  },\n  {\n    path: 'customers',\n    loadComponent: () => import('./components/customer-list/customer-list.component').then(m => m.CustomerListComponent),\n  },\n  {\n    path: 'customers/:selectedCustomerKey/users',\n    loadComponent: () => import('./components/user-list/user-list.component').then(m => m.UserListComponent),\n    canActivate: [userListGuard],\n  },\n  {\n    path: 'customers/:selectedCustomerKey/template-management',\n    loadComponent: () => import('./components/customer-templates/customer-templates.component').then(m => m.CustomerTemplatesComponent),\n    canActivate: [customerTemplateGuard],\n  },\n  {\n    path: 'usage-contracts',\n    loadComponent: () => import('./components/usage-contract-list/usage-contract-list.component').then(m => m.UsageContractListComponent),\n  },\n]", "fullText": "const routes: Routes = [\n  {\n    path: '',\n    pathMatch: 'full',\n    redirectTo: 'customers',\n  },\n  {\n    path: 'customers',\n    loadComponent: () => import('./components/customer-list/customer-list.component').then(m => m.CustomerListComponent),\n  },\n  {\n    path: 'customers/:selectedCustomerKey/users',\n    loadComponent: () => import('./components/user-list/user-list.component').then(m => m.UserListComponent),\n    canActivate: [userListGuard],\n  },\n  {\n    path: 'customers/:selectedCustomerKey/template-management',\n    loadComponent: () => import('./components/customer-templates/customer-templates.component').then(m => m.CustomerTemplatesComponent),\n    canActivate: [customerTemplateGuard],\n  },\n  {\n    path: 'usage-contracts',\n    loadComponent: () => import('./components/usage-contract-list/usage-contract-list.component').then(m => m.UsageContractListComponent),\n  },\n];"}, "error": null}]}