{"byModuleSpecifier": {"@angular/core": 68, "@angular/router": 37, "@angular/common/http": 1, "./user-signing-routing.module": 1, "./guards/user-signing-redirect.guard": 1, "./guards/user-signing.guard": 1, "./services/user-settings-helper.service": 1, "@fincloud/state/user": 1, "@ngrx/effects": 21, "@ngrx/store": 18, "./+state/effects/todos-management.effects": 1, "./+state/reducers/todos-management.reducer": 1, "./todos-management-routing.module": 1, "@fincloud/types/enums": 6, "./guards/check-todo-type.guard": 1, "./guards/check-todos-status.guard": 1, "./guards/find-todo-position.guard": 1, "./guards/load-businesscases.guard": 1, "./terms-and-conditions-routing.module": 1, "./support-routing.module": 1, "./login-routing.module": 1, "@fincloud/state/metareducers": 1, "./+state/login.effects": 1, "./+state/login.reducer": 1, "./guards/check-device-cookie.guard": 1, "./guards/confirm-device.guard": 1, "./guards/device-authentication-cookie-check.guard": 1, "./guards/device-authentication.guard": 1, "./guards/different-device.guard": 1, "./guards/email-authentication.guard": 1, "./guards/forgot-password.guard": 1, "./guards/get-password-page-data.guard": 1, "./guards/load-customer-keys.guard": 1, "./guards/load-customer.guard": 1, "./guards/lock-user.guard": 1, "./guards/login-success.guard": 1, "./guards/password-type.guard": 1, "./guards/redirection-error.guard": 1, "./guards/select-organization.guard": 1, "./guards/unlock-user.guard": 1, "./guards/user-authentication.guard": 1, "./guards/user-error.guard": 1, "./guards/user-validation.guard": 1, "@fincloud/state/utils": 7, "./+state": 7, "./+state/reducers/kpi.reducer": 1, "./kpis-routing.module": 1, "./guards/kpis-list.guard": 1, "./guards/duplicate-business-case.guard": 2, "@ng-bootstrap/ng-bootstrap": 2, "./+state/effects/duplicate-business-case.effects": 1, "./+state/reducers/duplicate-business-case.reducer": 1, "./duplicate-business-case.routing.module": 1, "./digital-signature-routing.module": 1, "./guards/redirect.guard": 1, "@angular/common": 7, "@fincloud/core/pipes": 5, "./+state/effects": 5, "./+state/reducers/dashboard.reducer": 1, "./dashboard-routing.module": 1, "./guards/dashboard.guard": 1, "./customer-master-data-routing.module": 1, "./contract-management-routing.module": 1, "./consent-routing.module": 1, "@angular/common/locales/de": 1, "./+state/effects/company-graph-documents.effects": 1, "./+state/effects/company-graph-modify-node.effects": 1, "./+state/effects/company-graph-network.effects": 1, "./+state/effects/company-graph-revisions.effects": 1, "./+state/effects/company-graph-settings.effects": 1, "./+state/effects/company-graph-update-changes.effects": 1, "./+state/effects/company-graph-visualization.effects": 1, "./+state/reducers/company-graph.reducer": 1, "./company-graph-routing.module": 1, "./jointjs/services/graph-gallery.service": 1, "./jointjs/services/graph.service": 1, "./jointjs/services/radial-graph-layout.service": 1, "./guards/company-graph.guard": 1, "@fincloud/state/document": 2, "@fincloud/ui/modal": 2, "./company-analysis-routing.module": 1, "./services/company-modal.service": 1, "@fincloud/neoshare/business-case": 2, "@fincloud/neoshare/guards": 6, "./+state/effects/demo-snapshot-common.effects": 1, "./+state/effects/demo-snapshot-copy.effects": 1, "./+state/effects/demo-snapshot-create.effects": 1, "./+state/effects/demo-snapshot-customers.effects": 1, "./+state/effects/demo-snapshot-delete.effects": 1, "./+state/effects/demo-snapshot-deploy.effects": 1, "./+state/effects/demo-snapshot-details-common.effects": 1, "./+state/effects/demo-snapshot-details-latest-state.effects": 1, "./+state/effects/demo-snapshot-edit-general-information.effects": 1, "./+state/effects/demo-snapshot-edit-user.effects": 1, "./+state/effects/demo-snapshot-list.effects": 1, "./+state/effects/demo-snapshot-recall.effects": 1, "./+state/effects/demo-snapshot-reset.effects": 1, "./+state/reducers/demo-snapshot-create.reducer": 1, "./+state/reducers/demo-snapshot-details.reducer": 1, "./+state/reducers/demo-snapshot.reducer": 1, "./cluster-demo-routing.module": 1, "./services/demo-snapshot-helper.service": 1, "./guards/snapshot-copy.guard": 1, "./guards/snapshot-create.guard": 1, "./guards/snapshot-details.guard": 1, "./guards/snapshot-edit.guard": 1, "./+state/effects/chat-export.effects": 1, "./+state/effects/chat.effects": 1, "./chat-routing.module": 1, "./cases-routing.module": 1, "./guards/cases.guard": 1, "./+state/effects/business-case-template-management.effects": 1, "./+state/reducers/business-case-template-management.reducer": 1, "./business-case-template-management-routing.module": 1, "./+state/effects/teaser-export.effects": 1, "./+state/reducers": 2, "./+state/reducers/teaser-export.reducer": 1, "./business-case-real-estate-financing-structure-routing.module": 1, "./services/refs-content.service": 1, "./services/refs-financing-partners.service": 1, "./services/search-financing.service": 1, "@fincloud/neoshare/neogpt-chat": 2, "./guards/financing-details-real-estate-activate.guard": 1, "./guards/financing-structure-deactivate.guard": 1, "./guards/financing-structure-resolver.guard": 1, "./guards/initial-shared-groups-resolver": 1, "./guards/refs-resolver": 1, "./guards/section-application-real-estate-match.guard": 1, "./guards/section-invitation-real-estate-match.guard": 1, "./guards/shared-financing-structure-activate.guard": 1, "./business-case-portal-routing.module": 1, "./business-case-miscellaneous-financing-structure-routing.module": 1, "./guards/financing-details-miscellaneous-activate.guard": 1, "./guards/participation-miscellaneous-match.guard": 1, "./guards/section-application-miscellaneous-match.guard": 1, "./guards/section-invitation-miscellaneous-match.guard": 1, "./business-case-management-routing.module": 1, "./components/business-case-management/business-case-management.component": 2, "./components/business-case-state/business-case-state.component": 1, "./components/collaboration-state-change-modal/collaboration-state-change-modal.component": 1, "./+state/effects/management-summary.effects": 1, "./business-case-kpi-routing.module": 1, "./guards/business-case-kpi-list.guard.guard": 1, "./+state/metareducers/business-case-inbox-storage-sync.metareducer": 1, "./business-case-inbox-routing.module": 1, "./utils/business-case-inbox-initial-state": 1, "./guards/business-case-inbox-resolver": 1, "./guards/close-document-classification-socket-connection.guard": 1, "@fincloud/state/folder-structure": 1, "@fincloud/state/key-information-extraction": 2, "./business-case-data-room-routing.module": 1, "./guards/access-linked-comapny.guard": 1, "./guards/load-company-data-room-folder-structures.guard": 1, "./guards/load-data-room-folder-structures.guard": 1, "@fincloud/state/business-case-real-estate": 1, "@fincloud/state/customer": 1, "@fincloud/state/faq": 2, "./business-case-dashboard-routing.module": 1, "@fincloud/state/business-case": 1, "./guards/can-activate-administration.guard": 1, "./guards/can-activate-business-case-overview.guard": 1, "./guards/can-activate-business-case.guard": 1, "./guards/can-activate-case-to-case-routing.guard": 1, "./guards/can-match-financing-structure-factory.guard": 1, "./guards/load-case-fields-access.guard": 1, "./guards/redirect-non-match.guard": 1, "./business-case-corporate-financing-structure-routing.module": 1, "./guards/corporate-financing-sructure-resolver": 1, "./guards/financing-details-corporate-activate.guard": 1, "./guards/financing-details-corporate-deactivate.guard": 1, "./guards/financing-structure-corporate-activate.guard": 1, "./guards/participation-corporate-match.guard": 1, "./guards/section-application-corporate-match.guard": 1, "./guards/section-invitation-corporate-match.guard": 1, "./+state/effects/business-case-collaboration.effects": 1, "./business-case-collaboration-routing.module": 1, "./guards/can-access-my-partner.guard": 1, "./guards/private-collaboration-invitations-applications.guard": 1, "./guards/private-collaboration.guard": 1, "./+state/effects/business-case-administration.effects": 1, "./+state/reducers/business-case-administration.reducer": 1, "./business-case-administration-routing.module": 1, "./guards/administration-integrations-activate.guard": 1, "./+state/reducers/apps-integrations.reducer": 1, "./guards/apps-integration.guard": 1, "./+state/effects/account-management.effects": 1, "./+state/reducers/account-management.reducer": 1, "./account-management-routing.module": 1, "./guards/customer-template.guard": 1, "./guards/user-list.guard": 1}, "byType": {"named": 370, "default": 1, "namespace": 5, "mixed": 0, "side-effect": 0}, "mostCommonImports": {"NgModule": 68, "Routes": 37, "provideEffects": 21, "provideState": 18, "effects": 5, "CurrencyPipe": 4, "DecimalPipe": 4, "RemoveTrailingZerosPipe": 4, "FinancingDetailsPath": 4, "PercentPipe": 3, "participationRouteGuard": 3, "initialState": 2, "InitialsPipe": 2, "DatePipe": 2, "StateLibDocumentEffects": 2, "FIN_MODAL_DEFAULT_OPTIONS": 2, "FIN_MODAL_REF_PROVIDER": 2, "FinModalService": 2, "TeaserExportEffects": 2, "neogptChatActivateGuard": 2}}